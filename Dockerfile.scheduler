ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-buster

ENV DEBUG=${DEBUG:-"False"}
ENV TZ=${TZ:-"America/Sao_Paulo"}

WORKDIR /app

COPY requirements.txt .
RUN apt-get update && apt-get install curl -y && apt-get clean && rm -rf /var/lib/apt/lists/*
RUN pip install -r requirements.txt

COPY ./src/worker /app/src/worker
COPY ./src/scheduler /app/src/scheduler
COPY ./src/connections /app/src/connections
COPY ./src/data /app/src/data
COPY ./src/extras /app/src/extras
COPY ./src/integrations /app/src/integrations
COPY ./tests /app/tests
COPY ./src/worker/.coveragerc /app/.coveragerc

ENV PYTHONPATH=/app:$PYTHONPATH
CMD ["python", "-u", "src/scheduler/entrypoint.py"]
