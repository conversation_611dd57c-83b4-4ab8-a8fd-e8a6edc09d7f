<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Tester</title>
    <style>
        :root {
            --background: #181a1b;
            --container-bg: #23272a;
            --input-bg: #2c2f33;
            --input-border: #444950;
            --text: #e6e6e6;
            --label: #b9bbbe;
            --primary: #4CAF50;
            --primary-hover: #388e3c;
            --success-bg: #22372b;
            --success-text: #8fd19e;
            --error-bg: #3a2323;
            --error-text: #ffb3b3;
            --user-bubble: #2e7d32;
            --assistant-bubble: #36393f;
            --bubble-text: #e6e6e6;
            --border: #36393f;
            --shadow: 0 0 10px rgba(0,0,0,0.5);
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--background);
            color: var(--text);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--container-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--label);
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            background: var(--input-bg);
            color: var(--text);
            border: 1px solid var(--input-border);
            border-radius: 4px;
            box-sizing: border-box;
        }
        input::placeholder, textarea::placeholder {
            color: #888;
        }
        button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background 0.2s;
        }
        button:hover {
            background-color: var(--primary-hover);
        }
        .message-container {
            margin-top: 30px;
            border-top: 1px solid var(--border);
            padding-top: 20px;
        }
        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            max-width: 70%;
        }
        .user-message {
            background-color: var(--user-bubble);
            color: var(--bubble-text);
            align-self: flex-end;
            margin-left: auto;
        }
        .assistant-message {
            background-color: var(--assistant-bubble);
            color: var(--bubble-text);
        }
        .messages-wrapper {
            display: flex;
            flex-direction: column;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: var(--success-bg);
            color: var(--success-text);
        }
        .error {
            background-color: var(--error-bg);
            color: var(--error-text);
        }
        .chat-bubble {
            position: relative;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
            color: var(--bubble-text);
        }
        .user {
            background-color: var(--user-bubble);
            margin-left: auto;
            margin-right: 10px;
            border-bottom-right-radius: 0;
        }
        .assistant {
            background-color: var(--assistant-bubble);
            margin-right: auto;
            margin-left: 10px;
            border-bottom-left-radius: 0;
        }
        .message-row {
            width: 100%;
            display: flex;
        }
        details {
            background: var(--assistant-bubble);
            color: var(--bubble-text);
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 5px 10px;
        }
        summary {
            cursor: pointer;
            font-weight: bold;
            color: var(--primary);
        }
        code {
            background: #22272e;
            color: #8fd19e;
            padding: 2px 5px;
            border-radius: 4px;
            font-family: "Fira Mono", "Consolas", monospace;
        }
        strong {
            color: #baffc9;
        }
        em {
            color: #ffd6a5;
        }
        u {
            text-decoration: underline;
        }
        del {
            color: #ffb3b3;
        }
        /* Scrollbar styling for dark mode */
        ::-webkit-scrollbar {
            width: 8px;
            background: #23272a;
        }
        ::-webkit-scrollbar-thumb {
            background: #36393f;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Message Tester</h1>

        <details>
            <summary id="shortcuts-summary">
                Atalhos disponíveis (Pressione Ctrl+Shift+A para abrir)
            </summary>
                <ul>
                    <li>Ctrl+Shift+I -> Editar o Instance ID </li>
                    <li>Ctrl+Shift+C -> Editar o Company ID </li>
                    <li>Ctrl+Shift+M -> Editar mensagem para envio </li>
                    <li>Ctrl+Shift+R -> Recarregar mensagens </li>
                    <li>Enter -> Enviar Mensagem </li>
                </ul>
        </details>
        <br><br>
        
        <div class="form-group">
            <label for="instanceId">Instance ID:</label>
            <input type="text" id="instanceId" value="3DB4A8AA8CA74006DC791E1C7791D352">
        </div>
        
        <div class="form-group">
            <label for="companyId">Company ID:</label>
            <input type="text" id="companyId" value="5297f17485f010616fcfd3a15099d7a5-1">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone Number (with country code and area code):</label>
            <input type="text" id="phone" placeholder="e.g. 5562981823352" value="5562981823352">
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" rows="4" placeholder="Type your message here"></textarea>
        </div>
        
        <button id="send-button" onclick="sendMessage()">Send Message</button>
        <button onclick="loadMessages()">Reload Messages</button>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="message-container">
            <h2>Messages</h2>
            <div id="messages" class="messages-wrapper"></div>
        </div>
    </div>

    <script>

        // Function to send a message
        async function sendMessage() {
            const instanceId = document.getElementById('instanceId').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;
            const companyId = document.getElementById('companyId').value;

            // Salva instanceId e companyId no localStorage
            localStorage.setItem('instanceId', instanceId);
            localStorage.setItem('companyId', companyId);

            if (!instanceId || !phone || !message) {
                showStatus('Please fill all required fields', 'error');
                return;
            }
            
            try {
                const body = {
                    isStatusReply: false,
                    chatLid: "148730825166915@lid",
                    connectedPhone: "556281305490",
                    waitingMessage: false,
                    isEdit: false,
                    isGroup: false,
                    isNewsletter: false,
                    instanceId: instanceId,
                    messageId: "E39ED711D3A6A8B057825B379A7519AF",
                    phone: phone,
                    fromMe: false,
                    momment: Date.now(),
                    status: "RECEIVED",
                    chatName: "Eu",
                    senderPhoto: null,
                    senderName: "User",
                    photo: "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIAcS8PzBJzfXfqMlBhFUaHqXzWH-vGsnIrBaSOiqu2fe&oe=66FABE25&_nc_sid=5e03e0&_nc_cat=100",
                    broadcast: false,
                    participantLid: null,
                    forwarded: false,
                    type: "ReceivedCallback",
                    fromApi: false,
                    text: {
                        message: message
                    }
                };

                // Using our Flask server as a proxy to avoid CORS issues
                const response = await fetch('/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(body)
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    showStatus('Message sent successfully!', 'success');
                    document.getElementById('message').value = '';
                    // Wait a moment for the system to process the message before loading
                    setTimeout(() => loadMessages(), 1000);
                } else {
                    showStatus(`Error: ${data.message || data.error || 'Failed to send message'}`, 'error');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        // Function to load messages from Redis
        async function loadMessages() {
            const phone = document.getElementById('phone').value;
            const companyId = document.getElementById('companyId').value;
            
            if (!phone || !companyId) {
                showStatus('Please enter phone number and company ID', 'error');
                return;
            }
            
            try {
                // Using our Flask server endpoint
                const response = await fetch(`/get-messages?phone=${phone}&companyId=${companyId}`);
                
                if (response.ok) {
                    const messages = await response.json();
                    displayMessages(messages);
                    showStatus('Messages loaded successfully', 'success');
                } else {
                    const error = await response.text();
                    showStatus(`Error loading messages: ${error}`, 'error');
                }
            } catch (error) {
                console.error('Error loading messages:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        // Function to display messages
        function displayMessages(messages) {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';
            
            if (!messages || messages.length === 0) {
                messagesContainer.innerHTML = '<p>No messages found.</p>';
                return;
            }
            
            messages.forEach(msg => {
                // se a mensagem começar com "Função chamada:", fazer um dropdown simples pra mostrar o restante
                if (msg.mensagem.startsWith('Função chamada:')) {
                    const dropdown = document.createElement('details');
                    const summary = document.createElement('summary');
                    summary.textContent = "Chamada de função";
                    dropdown.appendChild(summary);
                    
                    const pre = document.createElement('div');
                    pre.textContent = msg.mensagem;
                    pre.className = 'chat-bubble assistant';
                    dropdown.appendChild(pre);
                    
                    messagesContainer.appendChild(dropdown);
                    return;
                }
                const messageRow = document.createElement('div');
                messageRow.className = 'message-row';
                
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-bubble ${msg.enviado_por === 'user' ? 'user' : 'assistant'}`;
                
                // Convert markdown to HTML
                const markdownText = msg.mensagem;
                const htmlText = markdownToHtml(markdownText);
                messageDiv.innerHTML = htmlText;
                
                messageRow.appendChild(messageDiv);
                messagesContainer.appendChild(messageRow);
            });
        }

        // Function to convert markdown to HTML
        function markdownToHtml(markdown) {
            return markdown
            .replace(/</g, '&lt;') // Escape HTML tags
            .replace(/>/g, '&gt;')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
            .replace(/__(.*?)__/g, '<u>$1</u>') // Underline
            .replace(/~~(.*?)~~/g, '<del>$1</del>') // Strikethrough
            .replace(/`(.*?)`/g, '<code>$1</code>') // Inline code
            .replace(/\n/g, '<br>'); // Line breaks
        }

        // Function to show status messages
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // Atalhos de teclado e preenchimento automático do localStorage
        document.addEventListener('DOMContentLoaded', () => {
            const params = new URLSearchParams(window.location.search);
            const autoload = params.get('autoload');

            // Preenche instanceId e companyId do localStorage se existirem
            const savedInstanceId = localStorage.getItem('instanceId');
            const savedCompanyId = localStorage.getItem('companyId');
            if (savedInstanceId) document.getElementById('instanceId').value = savedInstanceId;
            if (savedCompanyId) document.getElementById('companyId').value = savedCompanyId;

            // Enter para enviar mensagem
            document.getElementById('message').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            if (autoload !== 'false') {
                loadMessages();
            }
        });

        // Atalhos de teclado extras
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+R para recarregar mensagens
            if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'r') {
                e.preventDefault();
                loadMessages();
            }
            // Ctrl+Shift+I para editar instanceId
            if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'i') {
                e.preventDefault();
                document.getElementById('instanceId').focus();
                document.getElementById('instanceId').select();
            }
            // Ctrl+Shift+C para editar companyId
            if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'c') {
                e.preventDefault();
                document.getElementById('companyId').focus();
                document.getElementById('companyId').select();
            }
            // Ctrl+Shift+M para editar mensagem
            if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'm') {
                e.preventDefault();
                document.getElementById('message').focus();
                document.getElementById('message').select();
            }
            if (
                e.ctrlKey &&
                e.shiftKey &&
                (e.key === 'A' || e.key === 'a')
            ) {
                const summary = document.getElementById('shortcuts-summary');
                if (summary) summary.click();
                e.preventDefault();
            }
        });

    </script>
</body>
</html>
