ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-buster

ENV DEBUG=${DEBUG:-"False"}
ENV TZ=${TZ:-"America/Sao_Paulo"}
ENV BOT_CONVERSA_API_KEY=${BOT_CONVERSA_API_KEY:-"c0b9d8e4-3595-4580-9fa8-0f1926bd7801"}
ENV Z_API_CLIENT_TOKEN=${Z_API_CLIENT_TOKEN:-"F3b14fd459eb9462ebd51b359510e2c4dS"}
ENV URL_PACTO_DISCOVERY_MS=${URL_PACTO_DISCOVERY_MS:-"https://discovery.ms.pactosolucoes.com.br/"}
ENV NUM_THREADS=${NUM_THREADS:-"4"}
ENV ROLES_TO_KEEP_REDIS=${ROLES_TO_KEEP_REDIS:-"assistant, user"}

WORKDIR /app

RUN apt-get update && apt-get install -y ffmpeg curl && apt-get clean
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install routellm

COPY ./src/worker /app/src/worker
COPY ./src/connections /app/src/connections
COPY ./src/data /app/src/data
COPY ./src/extras /app/src/extras
COPY ./src/integrations /app/src/integrations
COPY ./src/routerllm /app/src/routerllm

ENV PYTHONPATH=/app:$PYTHONPATH

CMD ["python", "-u", "src/routerllm/entrypoint.py"]
