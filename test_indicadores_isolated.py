#!/usr/bin/env python3
"""
Teste isolado para o endpoint de indicadores.
"""
import sys
import os
import json
from unittest.mock import patch, MagicMock

# Adicionar o diretório raiz ao PYTHONPATH
sys.path.insert(0, '/home/<USER>/Projects/orion')

def test_endpoint_isolated():
    """Testa o endpoint de indicadores de forma isolada."""
    
    # Mock das dependências
    with patch('src.extras.config.Config') as mock_config:
        mock_config.RATE_LIMIT_ENABLED = False
        mock_config.MODE = 'api'
        mock_config.REDIS_URL = 'redis://localhost:6379/0'
        mock_config.ALLOWED_ORIGINS = ['https://api.z-api.io']
        mock_config.AUTH_ON = 'false'  # Desabilitar auth para teste
        
        with patch('src.api.app.FlaskRedis'):
            with patch('src.connections.connections.Connections.get_instance') as mock_conn:
                mock_redis = MagicMock()
                mock_conn.return_value.redis_client = mock_redis
                
                with patch('src.extras.util.register_indicator') as mock_register:
                    mock_register.return_value = None
                    
                    # Importar e criar a app
                    from src.api.app import create_app
                    app = create_app()
                    
                    # Remover before_request para teste
                    app.before_request_funcs[None] = []
                    
                    # Criar cliente de teste
                    with app.test_client() as client:
                        print("=== TESTE 1: Dados válidos completos ===")
                        payload = {
                            'identificador': 'lead_criado',
                            'indicador': 'ativo',
                            'telefone': '5511999999999',
                            'nome': 'João Silva',
                            'meta': {'origem': 'api', 'campanha': 'black_friday'}
                        }
                        
                        response = client.post(
                            '/bi/indicadores',
                            json=payload,
                            content_type='application/json'
                        )
                        
                        print(f"Status: {response.status_code}")
                        print(f"Response: {response.get_json()}")
                        print(f"register_indicator chamado: {mock_register.called}")
                        if mock_register.called:
                            print(f"Argumentos: {mock_register.call_args}")
                        print()
                        
                        # Reset mock
                        mock_register.reset_mock()
                        
                        print("=== TESTE 2: Dados mínimos ===")
                        payload_minimal = {
                            'identificador': 'conversa_iniciada'
                        }
                        
                        response2 = client.post(
                            '/bi/indicadores',
                            json=payload_minimal,
                            content_type='application/json'
                        )
                        
                        print(f"Status: {response2.status_code}")
                        print(f"Response: {response2.get_json()}")
                        print(f"register_indicator chamado: {mock_register.called}")
                        if mock_register.called:
                            print(f"Argumentos: {mock_register.call_args}")
                        print()
                        
                        # Reset mock
                        mock_register.reset_mock()
                        
                        print("=== TESTE 3: Identificador vazio ===")
                        payload_empty = {
                            'identificador': '',
                            'indicador': 'ativo'
                        }
                        
                        response3 = client.post(
                            '/bi/indicadores',
                            json=payload_empty,
                            content_type='application/json'
                        )
                        
                        print(f"Status: {response3.status_code}")
                        print(f"Response: {response3.get_json()}")
                        print(f"register_indicator chamado: {mock_register.called}")
                        print()
                        
                        # Reset mock
                        mock_register.reset_mock()
                        
                        print("=== TESTE 4: JSON inválido ===")
                        response4 = client.post(
                            '/bi/indicadores',
                            data='invalid json',
                            content_type='application/json'
                        )
                        
                        print(f"Status: {response4.status_code}")
                        print(f"Response: {response4.get_json()}")
                        print(f"register_indicator chamado: {mock_register.called}")
                        print()
                        
                        print("=== TESTE 5: Sem identificador ===")
                        payload_no_id = {
                            'indicador': 'ativo'
                        }
                        
                        response5 = client.post(
                            '/bi/indicadores',
                            json=payload_no_id,
                            content_type='application/json'
                        )
                        
                        print(f"Status: {response5.status_code}")
                        print(f"Response: {response5.get_json()}")
                        print(f"register_indicator chamado: {mock_register.called}")

if __name__ == '__main__':
    test_endpoint_isolated()
