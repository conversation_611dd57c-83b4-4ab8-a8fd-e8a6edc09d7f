from tests.fluxos_pre_venda_normal.cadastro_novo_lead import (
    test_suite as test_suite_cadastro_novo_lead_normal,
)
from tests.fluxos_pre_venda_normal.informa_endereco_unidade import (
    test_suite as test_suite_informa_endereco_unidade_normal,
)
from tests.fluxos_pre_venda_normal.informa_dados_de_produtos import (
    test_suite as test_suite_informa_dados_de_produtos_normal,
)
from tests.fluxos_pre_venda_normal.informa_dados_de_aula import (
    test_suite as test_suite_informa_dados_de_aula_normal,
)
from tests.fluxos_pre_venda_normal.informa_dados_de_planos import (
    test_suite as test_suite_informa_dados_de_planos_normal,
)
from tests.fluxos_pre_venda_normal.notificacao_de_agendamento_de_aula_experimental import (
    test_suite as test_suite_notificacao_de_agendamento_de_aula_experimental_normal,
)
from tests.fluxos_pre_venda_normal.cadastro_visitante import (
    test_suite as test_suite_cadastro_visitante_normal,
)
from tests.fluxos_pre_venda_normal.agendamento_aula_experimental import (
    test_suite as test_suite_agendamento_aula_experimental_normal,
)
from tests.fluxos_pre_venda_normal.lead_solicita_agendamento_ligacao import (
    test_suite as test_suite_lead_solicita_agendamento_ligacao_normal,
)

__all__ = ["pre_venda_normal_tests"]

pre_venda_normal_tests = {
    test_suite_cadastro_novo_lead_normal().test_name: test_suite_cadastro_novo_lead_normal,
    test_suite_informa_endereco_unidade_normal().test_name: test_suite_informa_endereco_unidade_normal,
    test_suite_informa_dados_de_produtos_normal().test_name: test_suite_informa_dados_de_produtos_normal,
    test_suite_informa_dados_de_aula_normal().test_name: test_suite_informa_dados_de_aula_normal,
    test_suite_informa_dados_de_planos_normal().test_name: test_suite_informa_dados_de_planos_normal,
    test_suite_notificacao_de_agendamento_de_aula_experimental_normal().test_name: test_suite_notificacao_de_agendamento_de_aula_experimental_normal,
    test_suite_cadastro_visitante_normal().test_name: test_suite_cadastro_visitante_normal,
    test_suite_agendamento_aula_experimental_normal().test_name: test_suite_agendamento_aula_experimental_normal,
    test_suite_lead_solicita_agendamento_ligacao_normal().test_name: test_suite_lead_solicita_agendamento_ligacao_normal,
}
