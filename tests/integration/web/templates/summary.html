<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Resultado dos Testes</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}" />
</head>
<body>
  <div class="container">
    <h1>Resultado dos Testes</h1>

    <div>
        <a href="/">Painel</a>
    </div>
    <br>

    {% for test in assertions %}
      <div class="test-summary">
        <h2>{{ test.test_name.replace("_", " ").title() }}</h2>
        <ul class="assertions-list">
            {% for key, assertion in (test.assertions or {}).items() %}
            <li class="assertion-item {% if assertion.value %}pass{% else %}fail{% endif %}">
              <span class="assertion-status">
                {% if assertion.value %}
                  <span class="status-pass">✔️</span>
                {% else %}
                  <span class="status-fail">❌</span>
                {% endif %}
              </span>
              <span class="assertion-name">{{ assertion.name }}</span>
              <span class="assertion-message">{{ assertion.message }}</span>
            </li>
          {% endfor %}
        </ul>
      </div>
    {% endfor %}

  </div>
</body>
</html>
