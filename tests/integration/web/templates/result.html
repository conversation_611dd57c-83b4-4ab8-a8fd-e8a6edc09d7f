<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resultado</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <h2>{{ result.test_name.replace("_", " ").title() }}</h2>
    <div class="section">
        <h2>Mensagens</h2>
        <ul class="messages">
            {% for msg in result.messages %}
            <li>{{ msg }}</li>
            {% endfor %}
        </ul>
    </div>
    <div class="section">
        <h2>Responses</h2>
        <ul class="responses">
            {% for resp in result.responses %}
            <li>{{ resp }}</li>
            {% endfor %}
        </ul>
    </div>
    {% for assert in result.asserts %}
    <div class="section asserts {% if 'failed' in assert.lower() or 'error' in assert.lower() %}fail{% else %}pass{% endif %}">
        {{ assert }}
    </div>
    {% endfor %}
    <div>
        <a href="/">Painel</a>
    </div>
</body>
</html>

