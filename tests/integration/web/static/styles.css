/* <PERSON><PERSON> e variáveis - WhatsApp Style */
:root {
  --color-outside-bg: #E5DDD5; /* WhatsApp chat background */
  --color-surface: #ffffff;
  --color-primary: #25D366; /* WhatsApp green */
  --color-primary-hover: #128C7E; /* Darker WhatsApp green */
  --color-text: #333333;
  --color-muted: #666666;
  --shadow-light: 0 1px 2px rgba(0, 0, 0, 0.1); /* Softer shadows like WhatsApp */
  --shadow-deep: 0 2px 8px rgba(0, 0, 0, 0.15);
  --radius: 8px;
  --spacing-unit: 1rem;
  --font-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* WhatsApp-like font stack */
}

/* Global reset */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  background-color: var(--color-outside-bg);
  min-height: 100vh;
  font-family: var(--font-base);
  color: var(--color-text);
  line-height: 1.6;
}

body {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: calc(var(--spacing-unit) * 2);
}

/* Container centralizado */
.container {
  width: 100%;
  max-width: 780px;
  background-color: var(--color-surface);
  padding: calc(var(--spacing-unit) * 1.5);
  border-radius: var(--radius);
  box-shadow: var(--shadow-light);
  transition: box-shadow 0.3s ease;
}

.container:hover {
  box-shadow: var(--shadow-deep);
}

/* Títulos */
h1, h2, h3 {
  margin-bottom: var(--spacing-unit);
  color: var(--color-text);
  font-weight: 600;
  letter-spacing: 0.5px;
}

h1 {
  font-size: 2rem;
  text-align: center;
}

/* Parágrafos */
p {
  margin-bottom: var(--spacing-unit);
}

/* Formulários */
.form-group {
  margin-bottom: calc(var(--spacing-unit) * 1.25);
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-muted);
  font-size: 0.95rem;
  letter-spacing: 0.4px;
  transition: color 0.2s ease;
}

input[type="text"], input[type="email"], textarea, select {
  width: calc(100% - 50px);
  padding: 10px 16px;
  border: 1px solid #D1D1D1;
  border-radius: 24px;
  font-family: inherit;
  font-size: 14px;
  background-color: #FFFFFF;
  margin-right: 8px;
  transition: border-color 0.2s ease-in-out;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.15);
}

.form-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.send-message-btn {
  background-color: #25D366;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
}

/* Botões - WhatsApp Style */
button {
  display: inline-block;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background: var(--color-primary);
  border: none;
  border-radius: 24px; /* More rounded like WhatsApp */
  cursor: pointer;
  transition: background 0.2s ease-in-out, transform 0.1s ease;
}

button:hover {
  background: var(--color-primary-hover);
}



button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
}

/* Status */
.status {
  /* transforma em overlay fixo */
  position: fixed;
  top: calc(var(--spacing-unit) * 1);
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;

  /* mantém o estilo de bolha */
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #e0f2fe;
  color: var(--color-primary);
  border-radius: 999px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  font-weight: 500;
  text-align: center;
  line-height: 1.4;
  max-width: 80%;
  word-wrap: break-word;
}

/* seta na bolha */
.status::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px 8px 0 8px;
  border-style: solid;
  border-color: #e0f2fe transparent transparent transparent;
}

.message-container .chat-section {
  display: flex;
  gap: var(--spacing-unit);
}

.messages-wrapper {
  flex: 2;
  max-height: 50vh;
  overflow-y: auto;
  padding: 16px;
  background-color: #E5DDD5; /* WhatsApp chat background */
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: var(--radius);
  border: 1px solid #D1D1D1;
}

.logs-wrapper {
  flex: 1;
  max-height: 50vh;
  overflow-y: auto;
  padding: var(--spacing-unit);
  border: 1px solid #e5e7eb;
  border-radius: var(--radius);
  background-color: var(--color-surface);
  box-shadow: var(--shadow-light);
}

.message {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  background-color: #f9fafb;
  border-radius: var(--radius);
  line-height: 1.4;
}

.chat-bubble {
  position: relative;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  max-width: 65%;
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-bubble.user {
  background-color: #DCF8C6; /* WhatsApp outgoing message color */
  margin-left: auto;
  margin-right: 8px;
  border-bottom-right-radius: 2px; /* WhatsApp's tail effect */
}

.chat-bubble.assistant {
  background-color: #FFFFFF; /* WhatsApp incoming message color */
  margin-right: auto;
  margin-left: 8px;
  border-bottom-left-radius: 2px; /* WhatsApp's tail effect */
  border: 1px solid #E0E0E0;
}

.message-row {
  display: flex;
  width: 100%;
  align-items: flex-start;
}
/* textarea para cURL */
.log-curl {
  width: 100%;
  font-family: monospace;
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: var(--radius);
  background-color: #f9fafb;
  resize: vertical;
  min-height: 3rem;
}

/* botão de copiar */
.copy-btn {
  margin-top: 0.5rem;
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
  border: none;
  border-radius: var(--radius);
  background-color: var(--color-primary);
  color: #fff;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copy-btn:hover {
  background-color: var(--color-primary-hover);
}

.log-bubble.status-4xx {
  background-color: #FFF4E5; /* laranja claro */
}

.log-bubble.status-5xx {
  background-color: #FDECEA; /* vermelho claro */
}

.log-bubble.status-200 {
  background-color: #E6F4EA; /* verde claro */
}

/* modal inicia escondido */
.modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  z-index: 1000;
}

/* backdrop semi-transparente */
.modal .backdrop {
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.4);
}

/* janela central */
.modal .modal-content {
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-surface);
  padding: calc(var(--spacing-unit)*1.5);
  border-radius: var(--radius);
  box-shadow: var(--shadow-deep);
  width: 90%;
  max-width: 600px;
  max-height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* botão de fechar */
.close-modal {
  align-self: flex-end;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

/* quando aberto, exibe o modal */
.modal.is-open {
  display: block;
}

/* Checkbox Button Styles */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: var(--spacing-unit);
  margin-bottom: calc(var(--spacing-unit) * 1.25);
}

.checkbox-group input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-group label {
  position: relative;
  padding-left: calc(var(--spacing-unit) * 2);
  cursor: pointer;
  font-size: 1rem;
  color: var(--color-text);
  user-select: none;
  line-height: 1;
  transition: color 0.2s ease;
}

.checkbox-group label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  background-color: var(--color-surface);
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.checkbox-group label::after {
  content: "";
  position: absolute;
  left: 0.375rem;
  top: calc(50% + 0.125rem);
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-group input[type="checkbox"]:checked + label::before {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-group input[type="checkbox"]:checked + label::after {
  opacity: 1;
}

.checkbox-group input[type="checkbox"]:focus + label::before {
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.3);
}

.checkbox-group label:hover::before {
  border-color: var(--color-primary-hover);
}

.checkbox-group input[type="checkbox"]:disabled + label {
  color: var(--color-muted);
  cursor: not-allowed;
}

.checkbox-group input[type="checkbox"]:disabled + label::before {
  border-color: #ccc;
  background-color: #f9f9f9;
}

/* Link de volta */
a {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  border-radius: var(--radius);
  text-decoration: none;
  transition: background 0.2s ease-in-out, transform 0.1s ease, box-shadow 0.2s ease;
}

a:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--color-primary));
  transform: translateY(-1px);
}

a:active {
  transform: translateY(0);
}

a:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.3);
}

/* === Resumo de Testes === */
.test-summary {
  margin-bottom: var(--spacing-unit);
  padding: var(--spacing-unit);
  background-color: var(--color-surface);
  border-radius: var(--radius);
  box-shadow: var(--shadow-light);
}

.test-summary h2 {
  margin-bottom: calc(var(--spacing-unit) / 2);
  font-size: 1.25rem;
  color: var(--color-text);
}

.assertions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.assertion-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: var(--radius);
  background-color: #f9fafb;
  align-items: center;
  transition: background 0.2s;
}

/* status indicators */
.assertion-item.pass {
  border-left: 4px solid #34A853;   /* verde */
}
.assertion-item.fail {
  border-left: 4px solid #EA4335;   /* vermelho */
}

.assertion-item.pass:hover {
  background-color: #E6F4EA;        /* verde claro */
}
.assertion-item.fail:hover {
  background-color: #FDECEA;        /* vermelho claro */
}

.assertion-name {
  font-weight: 500;
  color: var(--color-text);
}

.assertion-message {
  color: var(--color-muted);
  font-size: 0.95rem;
  margin-left: var(--spacing-unit);
}

/* === Painel === */
#multiTestList {
  list-style: none;
  padding: 0;
  margin: var(--spacing-unit) 0;
}
#multiTestList li {
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: background 0.2s;
}
#multiTestList li:hover {
  background: var(--color-muted);
  color: #fff;
}

/* Responsividade */
@media (max-width: 768px) {
  body {
    padding: var(--spacing-unit);
  }

  .container {
    padding: var(--spacing-unit);
  }

  h1 {
    font-size: 1.5rem;
  }

  a {
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .messages-results li, .responses-results li {
    padding: 0.5rem;
  }
}

/* WhatsApp-style header */
.whatsapp-header {
  background-color: #075E54; /* WhatsApp dark green header */
  padding: 12px 16px;
  border-bottom: 1px solid #D1D1D1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius) var(--radius) 0 0;
}

.whatsapp-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF; /* White text for contrast */
}

.status-indicator {
  font-size: 12px;
  color: #FFFFFF;
  font-weight: 500;
}

/* Message input area - WhatsApp style */
.form-group {
  background-color: #F0F0F0;
  padding: 12px 16px;
  border-top: 1px solid #D1D1D1;
  border-radius: 0 0 var(--radius) var(--radius);
}

/* WhatsApp container styles */
.whatsapp-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: #FFFFFF;
  border-radius: var(--radius);
  box-shadow: var(--shadow-deep);
  overflow: hidden;
}

/* WhatsApp search bar */
.whatsapp-search {
  background-color: #F6F6F6;
  padding: 8px 16px;
  border-bottom: 1px solid #E0E0E0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  background-color: #FFFFFF;
  font-size: 14px;
}

/* WhatsApp contacts/tests list */
.whatsapp-contacts {
  background-color: #FFFFFF;
  overflow-y: auto;
  max-height: 70vh;
}

.test-list {
  display: flex;
  flex-direction: column;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #F2F2F2;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-item:hover {
  background-color: #F5F5F5;
}

.test-item.selected {
  background-color: #EBEBEB;
}

.test-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #25D366;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 16px;
}

.test-info {
  flex: 1;
}

.test-name {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 4px;
}

.test-description {
  font-size: 13px;
  color: #8C8C8C;
}

/* WhatsApp footer */
.whatsapp-footer {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  background-color: #F0F0F0;
  border-top: 1px solid #E0E0E0;
}

.whatsapp-button {
  background-color: #25D366;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.whatsapp-button:hover {
  background-color: #128C7E;
}

/* WhatsApp modal */
.whatsapp-modal {
  background-color: #FFFFFF;
  border-radius: 12px;
  max-width: 400px;
}

.whatsapp-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.whatsapp-list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F2F2F2;
  cursor: pointer;
}

.whatsapp-list-item:hover {
  background-color: #F5F5F5;
}

/* Header left with back button */
.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  color: white;
  font-size: 24px;
  text-decoration: none;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* WhatsApp button group */
.whatsapp-button-group {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin: 16px 0;
  padding: 0 16px;
}

/* Loading spinner animation */
.status {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #E0E0E0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(37, 211, 102, 0.3);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: #075E54;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Test folder organization */
.test-folder {
  border-bottom: 1px solid #E0E0E0;
}

.folder-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8F8F8;
  cursor: pointer;
  border-bottom: 1px solid #E0E0E0;
  transition: background-color 0.2s;
}

.folder-header:hover {
  background-color: #F0F0F0;
}

.folder-icon {
  margin-right: 12px;
  font-size: 18px;
}

.folder-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
  color: #075E54;
}

.folder-toggle {
  font-size: 12px;
  color: #8C8C8C;
  transition: transform 0.2s;
}

.folder-header.collapsed .folder-toggle {
  transform: rotate(-90deg);
}

.folder-content {
  max-height: 500px;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.folder-content.collapsed {
  max-height: 0;
}

.test-folder .test-item {
  padding-left: 48px;
}

/* Progress bar for test execution */
.progress-container {
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-size: 14px;
  font-weight: 500;
  color: #075E54;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 500;
  color: #25D366;
}

.progress-bar-track {
  width: 100%;
  height: 4px;
  background-color: #F0F0F0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #25D366;
  border-radius: 2px;
  transition: width 0.3s ease-in-out;
  width: 0%;
}

/* Enhanced loading animations */
.loading-container-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  padding: 16px;
  margin: 20px auto;
  width: 100%;
  max-width: 500px;
  box-sizing: border-box;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.loading-spinner-enhanced {
  width: 32px;
  height: 32px;
  border: 4px solid rgba(37, 211, 102, 0.2);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin-enhanced 1.2s ease-in-out infinite;
}

.loading-text-enhanced {
  font-size: 16px;
  font-weight: 500;
  color: #075E54;
  text-align: center;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes spin-enhanced {
  to { transform: rotate(360deg); }
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Test title styling */
.test-subtitle {
  font-size: 12px;
  color: #8C8C8C;
  font-weight: normal;
  margin-top: 2px;
}
