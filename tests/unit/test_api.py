# pylint: disable=too-many-lines
"""Testes unitários para a API."""
import os
import json
from uuid import uuid4
from unittest.mock import MagicMock, patch, call
from pytest import fixture, mark, raises

from src.api.app.exception.exception import Unauthorized
from src.api.app.before_request.before_request import before_request_routine
from src.api.app.auth.firebase_auth import handle_firebase_auth

with patch('src.extras.config.Config') as mock_config:
    mock_config.RATE_LIMIT_ENABLED = False
    mock_config.MODE = 'api'
    mock_config.REDIS_URL = 'redis://localhost:6379/0'
    mock_config.ALLOWED_ORIGINS = 'https://api.z-api.io'
    mock_config.AUTH_ON = 'true'
    from src.api.app import create_app
    from src.extras.util import parse_phone

API_MASTER_KEY = os.getenv('API_MASTER_KEY')

class TestAPI:
    """Classe de testes unitários para a API."""
    @fixture(autouse=True, scope='class')
    def log_requests_connections(self):
        """Fixture para log de requests."""
        with patch('src.api.app.before_request.before_request.CONNECTIONS') as mock:
            mock.return_value = MagicMock()
            yield mock

    @fixture(autouse=True, scope='class')
    def mode(self):
        """Fixture pro Config retornar mode = 'api'."""
        self.app.config['MODE'] = 'api'

    @fixture(autouse=True, scope='class')
    def get_api_key_integration(self):
        """Fixture para get_api_key_integration."""
        with patch('src.data.bigquery_data.BigQueryData.get_api_key_integration') as mock_get_api_key_integration:
            mock_get_api_key_integration.return_value = (self.api_key, 'pytest')
            yield mock_get_api_key_integration

    # pylint: disable=W0201
    def setup_class(self):
        """Configuração de testes."""
        with patch('src.api.app.FlaskRedis'):
            self.app = create_app()
            self.app.before_request_funcs[None] = []
            self.client = self.app.test_client()

        with patch('src.api.app.before_request.before_request.CONNECTIONS') as mock_connections:
            mock_connections.return_value = MagicMock()

            # api_key
            registration_headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {API_MASTER_KEY}',
            }
            registration_body = {'user': 'pytest', 'empresa': 'pytest-1'}


            self.api_key = self.client.post(
                '/register/', headers=registration_headers, json=registration_body
                ).json['api_key']
            self.app.redis_client.get.return_value = json.dumps({'integration': 'pytest'})
            with patch('src.data.bigquery_data.BigQueryData.get_api_key_integration') as mock_get_api_key_integration:
                mock_get_api_key_integration.return_value = (self.api_key, 'pytest')

                self.token = self.client.post(
                    '/auth/',
                    json={'api_key': self.api_key, 'empresa': 'pytest-1'},
                    ).json['auth_token']
                self.valid_headers = {
                    'Authorization': f'Bearer {self.token}'
                    }

    def test_index(self):
        """Testa a rota de index."""
        with self.app.test_client() as client:
            response = client.get('/')
            assert response.status_code == 200
            assert response.json == {}

    def test_health(self):
        """Testa a rota de health."""
        response = self.client.get('/health/')
        assert response.status_code == 200
        assert response.json.get("success", "fail") == "success"

    def test_auth(self):
        """Testa a rota de autenticação."""
        response = self.client.post(
            '/auth/',
            json={'api_key': self.api_key, 'empresa': 'pytest-1'},
            )
        assert response.status_code == 200
        assert 'auth_token' in response.json

    @patch('src.extras.util.register_indicator')
    def test_register_indicator_success(self, mock_register_indicator):
        """Testa o registro de indicador com dados válidos."""
        mock_register_indicator.return_value = None

        payload = {
            'identificador': 'lead_criado',
            'indicador': 'ativo',
            'telefone': '5511999999999',
            'nome': 'João Silva',
            'meta': {'origem': 'api', 'campanha': 'black_friday'}
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 200
        assert response.json['message'] == 'Indicador registrado com sucesso'
        assert response.json['data']['identificador'] == 'lead_criado'
        assert response.json['data']['indicador'] == 'ativo'
        assert response.json['data']['telefone'] == '5511999999999'
        assert response.json['data']['nome'] == 'João Silva'
        assert response.json['data']['meta'] == {'origem': 'api', 'campanha': 'black_friday'}

        # Verifica se a função register_indicator foi chamada corretamente
        mock_register_indicator.assert_called_once_with(
            identificador='lead_criado',
            id_empresa='pytest-1',
            indicador='ativo',
            telefone='5511999999999',
            nome='João Silva',
            meta={'origem': 'api', 'campanha': 'black_friday'}
        )

    @patch('src.extras.util.register_indicator')
    def test_register_indicator_minimal_data(self, mock_register_indicator):
        """Testa o registro de indicador com dados mínimos obrigatórios."""
        mock_register_indicator.return_value = None

        payload = {
            'identificador': 'conversa_iniciada'
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 200
        assert response.json['message'] == 'Indicador registrado com sucesso'
        assert response.json['data']['identificador'] == 'conversa_iniciada'
        assert response.json['data']['indicador'] == ''
        assert response.json['data']['telefone'] == ''
        assert response.json['data']['nome'] == ''
        assert response.json['data']['meta'] == {}

        # Verifica se a função register_indicator foi chamada corretamente
        mock_register_indicator.assert_called_once_with(
            identificador='conversa_iniciada',
            id_empresa='pytest-1',
            indicador='',
            telefone='',
            nome='',
            meta={}
        )

    def test_register_indicator_missing_identificador(self):
        """Testa o registro de indicador sem identificador obrigatório."""
        payload = {
            'indicador': 'ativo',
            'telefone': '5511999999999'
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 400
        assert 'identificador' in response.json['error']
        assert 'obrigatório' in response.json['error']

    def test_register_indicator_empty_identificador(self):
        """Testa o registro de indicador com identificador vazio."""
        payload = {
            'identificador': '',
            'indicador': 'ativo'
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 400
        assert 'identificador' in response.json['error']
        assert 'não vazia' in response.json['error']

    def test_register_indicator_invalid_json(self):
        """Testa o registro de indicador com JSON inválido."""
        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            data='invalid json'
        )

        assert response.status_code == 400
        assert 'JSON válido' in response.json['error']

    def test_register_indicator_invalid_data_types(self):
        """Testa o registro de indicador com tipos de dados inválidos."""
        payload = {
            'identificador': 'lead_criado',
            'indicador': 123,  # Deve ser string
            'telefone': True,  # Deve ser string
            'nome': [],  # Deve ser string
            'meta': 'invalid'  # Deve ser objeto
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 400
        assert 'deve ser uma string' in response.json['error'] or 'deve ser um objeto' in response.json['error']

    def test_register_indicator_unauthorized(self):
        """Testa o registro de indicador sem autenticação."""
        payload = {
            'identificador': 'lead_criado'
        }

        response = self.client.post(
            '/indicadores/',
            json=payload
        )

        assert response.status_code == 401

    @patch('src.extras.util.register_indicator')
    def test_register_indicator_internal_error(self, mock_register_indicator):
        """Testa o tratamento de erro interno."""
        mock_register_indicator.side_effect = Exception('Erro interno')

        payload = {
            'identificador': 'lead_criado'
        }

        response = self.client.post(
            '/indicadores/',
            headers=self.valid_headers,
            json=payload
        )

        assert response.status_code == 400
        assert 'Erro interno do servidor' in response.json['error']

    @mark.parametrize(
            'url, body, status_code, api_response',
            [
                (
                    '/enviar_mensagem/?empresa=pytest-1',
                    {},
                    400,
                    {'error': 'No data received'}
                ),
                (
                    '/enviar_mensagem/?empresa=pytest-1',
                    {'dummy': 'dummy'},
                    200,
                    {'success': 'success'}
                ),
                # Não é mais necessário pois o id da empresa é obtido pelo token
                # (
                #     '/enviar_mensagem/',
                #     {'dummy': 'dummy'},
                #     400,
                #     {'error': 'Missing empresa parameter'}
                # ),
            ]

    )
    def test_enviar_mensagem(self, url, body, status_code, api_response):
        """Testa a rota de enviar mensagem."""
        self.app.redis_client.reset_mock()
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
            )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('messages_queued', json.dumps({
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    def test_status_mensagem(self):
        """Testa o webhook status_mensagem."""
        self.app.redis_client.reset_mock()
        url = '/status_mensagem/'
        headers = self.valid_headers
        headers['Origin'] = 'https://api.z-api.io'
        headers['Content-Type'] = 'application/json'
        data = {
            "type": "MessageStatusCallback",
            "instanceId": "instance-1",
            "ids": ["id1", "id2"]
        }

        with patch('src.api.app.routes.status_mensagem.get_from_instance') as mock_get_from_instance:
            mock_get_from_instance.return_value = ('pytest-1', None)
            response = self.client.post(
                url,
                json=data,
                headers=headers
            )
            assert response.status_code == 200
            assert response.json == {"success": "success"}
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    "type": "status_mensagem",
                    "id_empresa": "pytest-1",
                    "data": {
                        "ids": ["id1", "id2"],
                        "instanceId": "instance-1",
                        "type": "MessageStatusCallback",
                        "id": "id1"
                    }
                }))],
                [call('task_queue_bd_contexts', json.dumps({
                    "type": "status_mensagem",
                    "id_empresa": "pytest-1",
                    "data": {
                        "ids": ["id1", "id2"],
                        "instanceId": "instance-1",
                        "type": "MessageStatusCallback",
                        "id": "id2"
                    }
                }))]
            )

    @mark.parametrize(
            'url, origin, body, status_code, is_running, model_source, api_response, ',
            [
                (
                    '/receber_mensagem/',
                    'https://api.z-api.io',
                    {},
                    400,
                    False,
                    'openai',
                    {'error': 'No data received'}
                ),
                (
                    '/receber_mensagem/',
                    'UNKNOWN',
                    {},
                    403,
                    False,
                    'openai',
                    {'error': 'Forbidden: Origin not allowed'}
                ),
                (
                    '/receber_mensagem/',
                    'https://api.z-api.io',
                    {'chave_empresa': 'pytest-1', 'instanceId': 'instance-1'},
                    200,
                    False,
                    'openai',
                    {'success': 'success'}
                ),
                (
                    '/receber_mensagem/',
                    'https://api.z-api.io',
                    {'chave_empresa': 'pytest-1', 'instanceId': 'instance-1'},
                    200,
                    True,
                    'routerllm',
                    {'success': 'success'}
                ),
                (
                    '/receber_mensagem/',
                    'https://api.z-api.io',
                    {'chave_empresa': 'pytest-1', 'instanceId': 'instance-1'},
                    200,
                    False,
                    'routerllm',
                    {'success': 'success'}
                )
            ]
    )
    def test_receber_mensagem(
        self,
        url,
        origin,
        body,
        status_code,
        is_running,
        model_source,
        api_response
        ):
        """Testa a rota de receber mensagem."""
        self.app.redis_client.reset_mock()
        headers = self.valid_headers
        headers['Origin'] = origin
        with patch('src.api.app.routes.receber_mensagem.bq') as mock_bq:
            mock_bq.return_value.get_model_source.return_value = model_source
            with patch('src.api.app.routes.receber_mensagem.is_running') as mock_is_running:
                mock_is_running.return_value = is_running
                with patch('src.api.app.routes.receber_mensagem.get_from_instance') as mock_get_from_instance:
                    mock_get_from_instance.return_value = ('pytest-1', None)
                    with patch('src.api.app.routes.receber_mensagem.inject') as mock_inject:
                        mock_inject.side_effect = lambda carrier: carrier.update({
                            "traceparent": "00-f689cae7756b175017f216824a58c953-1de2b29a536c4d27-01"
                        })
                        with patch('src.api.app.routes.receber_mensagem.uuid4') as mock_uuid:
                            with patch('src.api.app.routes.receber_mensagem.datetime') as mock_datetime:
                                now = 123
                                test_uuid = uuid4()
                                mock_uuid.return_value = test_uuid
                                mock_datetime.now.return_value.timestamp.return_value = 123
                                response = self.client.post(
                                    url,
                                    json=body,
                                    headers=headers
                                )

        expected_data = body.copy()
        if body:
            # The TaskAdapter might modify the data
            expected_data.update({
                "sessionId": None,
                "departamento": None,
                "colaborador": None
            })

        expected_message = {
            'id_empresa': 'pytest-1',
            'data': expected_data,
            'sessionId': None,
            'canAnswer': True,
            'origin': 'z_api',
            'task_uid': f"receber_mensagem_{str(test_uuid)[:8]}",
            'momento_recebimento': 123,
            'context': {
                'traceparent': '00-f689cae7756b175017f216824a58c953-1de2b29a536c4d27-01'
            }
        }

        assert response.status_code == status_code
        assert response.json == api_response
        if is_running:
            queue = (
                'messages_received_routerllm'
                if model_source == 'routerllm'
                else 'messages_received'
            )
            self.app.redis_client.lpush.assert_has_calls(
                [call(queue, json.dumps(expected_message))]
            )

    @mark.parametrize(
            'url, body, status_code, api_response',
            [
                (
                    '/atualizar_contexto_academia/',
                    {},
                    400,
                    {'error': 'BadRequest: ID da academia não informado'}
                ),
                (
                    '/atualizar_contexto_academia/?empresa=pytest-1',
                    {},
                    400,
                    {'error': 'BadRequest: Dados não informados'}
                ),
                (
                    '/atualizar_contexto_academia/?empresa=pytest-1',
                    {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                    200,
                    {'success': 'success'}
                )
            ]
    )
    def test_atualizar_contexto_academia(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto da academia."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
            )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'gym',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
            'url, body, status_code, api_response',
            [
                (
                    '/atualizar_contexto_planos/',
                    {},
                    400,
                    {'error': 'BadRequest: ID da academia não informado'}
                ),
                (
                    '/atualizar_contexto_planos/?empresa=pytest-1',
                    {},
                    400,
                    {'error': 'BadRequest: Dados não informados'}
                ),
                (
                    '/atualizar_contexto_planos/?empresa=pytest-1',
                    {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                    200,
                    {'success': 'success'}
                )
            ]
    )
    def test_atualizar_contexto_planos(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto de planos."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
            )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'plans',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
            'url, body, status_code, api_response',
            [
                (
                    '/atualizar_contexto_fases/',
                    {},
                    400,
                    {'error': 'BadRequest: ID da academia não informado'}
                ),
                (
                    '/atualizar_contexto_fases/?empresa=pytest-1',
                    {},
                    400,
                    {'error': 'BadRequest: Dados não informados'}
                ),
                (
                    '/atualizar_contexto_fases/?empresa=pytest-1',
                    {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                    200,
                    {'success': 'success'}
                )
            ]
    )
    def test_atualizar_contexto_fases(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto de fases."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
            )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'phases',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/atualizar_contexto_turmas/',
                {},
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/atualizar_contexto_turmas/?empresa=pytest-1',
                {},
                400,
                {'error': 'BadRequest: Dados não informados'}
            ),
            (
                '/atualizar_contexto_turmas/?empresa=pytest-1',
                {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_atualizar_contexto_turmas(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto de turmas."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
        )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'classes',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/atualizar_contexto_produtos/',
                {},
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/atualizar_contexto_produtos/?empresa=pytest-1',
                {},
                400,
                {'error': 'BadRequest: Dados não informados'}
            ),
            (
                '/atualizar_contexto_produtos/?empresa=pytest-1',
                {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_atualizar_contexto_produtos(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto de produtos."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
        )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'products',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/atualizar_contexto_personalidade/',
                {},
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/atualizar_contexto_personalidade/?empresa=pytest-1',
                {},
                400,
                {'error': 'BadRequest: Dados não informados'}
            ),
            (
                '/atualizar_contexto_personalidade/?empresa=pytest-1',
                {'academia_id': 'pytest-1', 'contexto': 'pytest'},
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_atualizar_contexto_personalidade(self, url, body, status_code, api_response):
        """Testa a rota de atualizar contexto de personalidade."""
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
        )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'personality',
                    'id_empresa': 'pytest-1',
                    'data': body
                }))]
            )

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_academia/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_academia/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_academia(self, url, status_code, api_response):
        """Testa a rota de consultar contexto da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_gym_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    # contexto usuário, tem que ter id_empresa, telefone, e data
    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/atualizar_contexto_usuario/',
                {},
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/atualizar_contexto_usuario/?empresa=pytest-1',
                {'aluno': 'pytest', 'fase_crm': 'pytest'},
                400,
                {'error': 'BadRequest: Telefone não informado'}
            ),
            (
                '/atualizar_contexto_usuario/?empresa=pytest-1&telefone=5562988887777',
                {},
                400,
                {'error': 'BadRequest: Dados não informados'}
            ),
            (
                '/atualizar_contexto_usuario/?empresa=pytest-1&telefone=5562988887777',
                {'aluno': 'pytest', 'fase_crm': 'pytest'},
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_atualizar_contexto_aluno(self, body, url, status_code, api_response):
        """Testa a rota de atualizar contexto dos alunos da academia."""
        self.app.redis_client.reset_mock()
        response = self.client.post(
            url,
            json=body,
            headers=self.valid_headers
        )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.lpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'user',
                    'id_empresa': 'pytest-1',
                    'data': {
                        'telefone': parse_phone('5562988887777'),
                        'contexto': json.dumps(body),
                        'fase': body.get('fase_crm'),
                        'origin_last_update': 'api'
                        }
                }))]
            )
        else:
            self.app.redis_client.lpush.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_planos/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_planos/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_planos(self, url, status_code, api_response):
        """Testa a rota de consultar contexto dos planos da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_plans_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_fases/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_fases/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_fases(self, url, status_code, api_response):
        """Testa a rota de consultar contexto das fases da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_phase_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_turmas/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_turmas/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_turmas(self, url, status_code, api_response):
        """Testa a rota de consultar contexto das turmas da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_classes_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_produtos/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_produtos/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_produtos(self, url, status_code, api_response):
        """Testa a rota de consultar contexto dos produtos da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_products_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_personalidade/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_personalidade/?empresa=pytest-1',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_personalidade(self, url, status_code, api_response):
        """Testa a rota de consultar contexto da personalidade da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_personality_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/consultar_contexto_aluno/',
                400,
                {'error': 'BadRequest: ID da academia não informado'}
            ),
            (
                '/consultar_contexto_aluno/?empresa=pytest-1',
                400,
                {'error': 'BadRequest: Telefone não informado'}
            ),
            (
                '/consultar_contexto_aluno/?empresa=pytest-1&telefone=5562988887777',
                200,
                {'pytest': 'pytest'}
            )
        ]
    )
    def test_consultar_contexto_aluno(self, url, status_code, api_response):
        """Testa a rota de consultar contexto dos alunos da academia."""
        with patch('src.api.app.routes.consultar_contexto.bq') as mock_bq:
            mock_bq.return_value.get_user_context.return_value = {'pytest': 'pytest'}
            response = self.client.get(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_academia/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_academia/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_academia(self, url, status_code, api_response):
        """Testa a rota de apagar contexto da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with('gym_context-pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_planos/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_planos/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )

    def test_apagar_contexto_planos(self, url, status_code, api_response):
        """Testa a rota de apagar contexto dos planos da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with('plans_context-pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_fases/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_fases/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_fases(self, url, status_code, api_response):
        """Testa a rota de apagar contexto das fases da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with('phases_context-pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_turmas/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_turmas/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_turmas(self, url, status_code, api_response):
        """Testa a rota de apagar contexto das turmas da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with('classes_context-pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_produtos/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_produtos/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_produtos(self, url, status_code, api_response):
        """Testa a rota de apagar contexto dos produtos da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with('products_context-pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_personalidade/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_personalidade/?empresa=pytest-1',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_personalidade(self, url, status_code, api_response):
        """Testa a rota de apagar contexto da personalidade da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_any_call('personality_context-pytest-1')
                self.app.redis_client.delete.assert_any_call('voice_schedule:pytest-1')
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_contexto_aluno/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_contexto_aluno/?empresa=pytest-1',
                400,
                {'error': 'BadRequest: Telefone não informado'}
            ),
            (
                '/apagar_contexto_aluno/?empresa=pytest-1&telefone=5562988887777',
                200,
                {'success': 'success'}
            ),
            (
                '/apagar_contexto_aluno/?empresa=pytest-1&telefone=5562988887777&reset_chat=true',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_contexto_aluno(self, url, status_code, api_response):
        """Testa a rota de apagar contexto dos alunos da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_any_call(
                    f"{parse_phone('5562988887777')}-pytest-1"
                    )
                if 'reset_chat' in url:
                    self.app.redis_client.delete.assert_any_call(
                        f"last_messages-{parse_phone('5562988887777')}-pytest-1"
                        )
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    @mark.parametrize(
        'url, status_code, api_response',
        [
            # (
            #     '/apagar_strikes_aluno/',
            #     400,
            #     {'error': 'BadRequest: ID da academia não informado'}
            # ),
            (
                '/apagar_strikes_aluno/?empresa=pytest-1',
                400,
                {'error': 'BadRequest: Telefone não informado'}
            ),
            (
                '/apagar_strikes_aluno/?empresa=pytest-1&telefone=5562988887777',
                200,
                {'success': 'success'}
            )
        ]
    )
    def test_apagar_strikes_aluno(self, url, status_code, api_response):
        """Testa a rota de apagar strikes dos alunos da academia."""
        with patch('src.api.app.routes.apagar_contexto.bq') as mock_bq:
            self.app.redis_client.reset_mock()
            response = self.client.delete(
                url,
                headers=self.valid_headers
            )
            assert response.status_code == status_code
            assert response.json == api_response
            if api_response.get('success'):
                self.app.redis_client.delete.assert_called_with(
                    f"strikes:{parse_phone('5562988887777')}-pytest-1"
                    )
            else:
                self.app.redis_client.delete.assert_not_called()
                mock_bq.return_value.execute_query.assert_not_called()

    # empresa
    @mark.parametrize(
        'url, status_code, bq_return, api_response',
        [
            (
                '/empresa',
                200,
                {'empresa': 'pytest'},
                {'empresa': 'pytest'}
            ),
            (
                '/empresa',
                404,
                None,
                {'error': 'not found'}
            )
        ]
    )
    def test_get_empresa(self, url, status_code, bq_return, api_response):
        """Testa a rota de consultar empresa."""
        with patch('src.api.app.auth.utils.auth_wrappers.JWT') as mock_jwt:
            mock_jwt.decode_auth_token.return_value = {
                'firebase': 'pytest-1',
                'user_id': 'pytest-1'
                }
            with patch(
                'src.api.app.auth.utils.auth_wrappers.handle_firebase_auth'
                ) as mock_handle_firebase_auth:
                mock_handle_firebase_auth.return_value = True
                with patch(
                    'src.api.app.routes.v2.empresa.bq'
                    ) as mock_bq:
                    mock_bq.return_value.get_empresa.return_value = bq_return
                    response = self.client.get(
                        url,
                        headers=self.valid_headers
                    )
        assert response.status_code == status_code
        assert response.json == api_response

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/empresa',
                {},
                400,
                {'error': 'BadRequest: Dados inválidos'}
            ),
            (
                '/empresa',
                {
                    'nomeFantasia': 'pytest'
                },
                400,
                {'error': 'BadRequest: Dados inválidos'}
            ),
            (
                '/empresa',
                {
                    'cep': 'pytest',
                    'cidade': 'pytest',
                    'cnpj': 'pytest',
                    'email': 'pytest',
                    'endereco': 'pytest',
                    'estado': 'pytest',
                    'horarioFuncionamento': 'pytest',
                    'nomeFantasia': 'pytest',
                    'site': 'pytest',
                    'telefone': 'pytest'
                },
                201,
                {
                    'id_empresa': 'pytest-1',
                    'success': 'success',
                }
            )
        ]
    )
    def test_create_empresa(self, url, body, status_code, api_response):
        """Testa a rota de criar empresa."""
        self.app.redis_client.reset_mock()
        with patch('src.api.app.auth.utils.auth_wrappers.JWT') as mock_jwt:
            mock_jwt.decode_auth_token.return_value = {
                'firebase': 'pytest-1',
                'user_id': 'pytest-1'
                }
            with patch(
                'src.api.app.auth.utils.auth_wrappers.handle_firebase_auth'
                ) as mock_handle_firebase_auth:
                mock_handle_firebase_auth.return_value = True
                response = self.client.post(
                    url,
                    json=body,
                    headers=self.valid_headers
                )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.rpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'api-v2',
                    'id_empresa': 'pytest-1',
                    'data': body,
                    'action': 'create',
                    'resource': 'empresa'
                }))]
            )

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/empresa',
                {},
                400,
                {'error': 'BadRequest: Dados inválidos'}
            ),
            (
                '/empresa',
                {
                    'nomeFantasia': 'pytest'
                },
                400,
                {'error': 'BadRequest: Dados inválidos'}
            ),
            (
                '/empresa',
                {
                    'cep': 'pytest',
                    'cidade': 'pytest',
                    'cnpj': 'pytest',
                    'email': 'pytest',
                    'endereco': 'pytest',
                    'estado': 'pytest',
                    'horarioFuncionamento': 'pytest',
                    'nomeFantasia': 'pytest',
                    'site': 'pytest',
                    'telefone': 'pytest'
                },
                200,
                {
                    'id_empresa': 'pytest-1',
                    'success': 'success',
                }
            )
        ]
    )
    def test_update_empresa(self, url, body, status_code, api_response):
        """Testa a rota de atualizar empresa."""
        self.app.redis_client.reset_mock()
        with patch('src.api.app.auth.utils.auth_wrappers.JWT') as mock_jwt:
            mock_jwt.decode_auth_token.return_value = {
                'firebase': 'pytest-1',
                'user_id': 'pytest-1'
                }
            with patch(
                'src.api.app.auth.utils.auth_wrappers.handle_firebase_auth'
                ) as mock_handle_firebase_auth:
                mock_handle_firebase_auth.return_value = True
                response = self.client.put(
                    url,
                    json=body,
                    headers=self.valid_headers
                )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.rpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'api-v2',
                    'id_empresa': 'pytest-1',
                    'data': body,
                    'action': 'update',
                    'resource': 'empresa'
                }))]
            )

    @mark.parametrize(
        'url, body, status_code, api_response',
        [
            (
                '/empresa',
                {},
                400,
                {'error': 'BadRequest: Dados inválidos'}
            ),
            (
                '/empresa',
                {'pytest': 'pytest'},
                200,
                {
                    'id_empresa': 'pytest-1',
                    'success': 'success',
                }
            )
        ]
    )
    def test_patch_empresa(self, url, body, status_code, api_response):
        """Testa a rota de atualizar empresa."""
        self.app.redis_client.reset_mock()
        with patch('src.api.app.auth.utils.auth_wrappers.JWT') as mock_jwt:
            mock_jwt.decode_auth_token.return_value = {
                'firebase': 'pytest-1',
                'user_id': 'pytest-1'
                }
            with patch(
                'src.api.app.auth.utils.auth_wrappers.handle_firebase_auth'
                ) as mock_handle_firebase_auth:
                mock_handle_firebase_auth.return_value = True
                response = self.client.patch(
                    url,
                    json=body,
                    headers=self.valid_headers
                )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.rpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'api-v2',
                    'id_empresa': 'pytest-1',
                    'data': body,
                    'action': 'patch',
                    'resource': 'empresa'
                }))]
            )

    @mark.parametrize(
        'url, status_code, api_response',
        [
            (
                '/empresa',
                200,
                {
                    'id_empresa': 'pytest-1',
                    'success': 'success',
                }
            )
        ]
    )
    def test_delete_empresa(self, url, status_code, api_response):
        """Testa a rota de deletar empresa."""
        self.app.redis_client.reset_mock()
        with patch('src.api.app.auth.utils.auth_wrappers.JWT') as mock_jwt:
            mock_jwt.decode_auth_token.return_value = {
                'firebase': 'pytest-1',
                'user_id': 'pytest-1'
                }
            with patch(
                'src.api.app.auth.utils.auth_wrappers.handle_firebase_auth'
                ) as mock_handle_firebase_auth:
                mock_handle_firebase_auth.return_value = True
                response = self.client.delete(
                    url,
                    headers=self.valid_headers
                )
        assert response.status_code == status_code
        assert response.json == api_response
        if api_response.get('success'):
            self.app.redis_client.rpush.assert_has_calls(
                [call('task_queue_bd_contexts', json.dumps({
                    'type': 'api-v2',
                    'id_empresa': 'pytest-1',
                    "data": "",
                    'action': 'delete',
                    'resource': 'empresa'
                }))]
            )

    def test_before_request_routine(self):
        """Testa a rotina before_request."""
        with patch('src.api.app.before_request.before_request.CONNECTIONS.redis_client') as mock_redis:
            with self.app.test_client() as client:
                self.app.before_request_funcs[None] = [before_request_routine]
                client.get('/', headers=self.valid_headers)
                mock_redis.rpush.assert_called_once()
                args, kwargs = mock_redis.rpush.call_args
                assert args[0] == 'logs'
                log_entry = json.loads(args[1])
                assert log_entry['table'] == 'requests_conversas_ai'
                assert log_entry['data']['path'] == '/'
                self.app.before_request_funcs[None] = []

    @patch('src.api.app.auth.google_oauth.get_google_provider_cfg')
    @patch('src.api.app.auth.google_oauth.client')
    def test_google_login(self, mock_client, mock_get_google_provider_cfg):
        """Testa o login com GoogleOauth."""
        mock_get_google_provider_cfg.return_value = {
            "authorization_endpoint": "https://accounts.google.com/o/oauth2/auth"
        }
        mock_client.prepare_request_uri.return_value = "https://accounts.google.com/o/oauth2/auth?response_type=code"

        with patch('src.api.app.auth.google_oauth.redirect') as mock_redirect:
            _response = self.client.get('/login')
            mock_redirect.assert_called_once_with("https://accounts.google.com/o/oauth2/auth?response_type=code")

    @patch('src.api.app.auth.google_oauth.get_google_provider_cfg')
    @patch('src.api.app.auth.google_oauth.client')
    @patch('src.api.app.auth.google_oauth.requests.post')
    @patch('src.api.app.auth.google_oauth.requests.get')
    @patch('src.api.app.auth.google_oauth.User')
    @patch('src.api.app.auth.google_oauth.login_user')
    def test_google_callback(
        self,
        mock_login_user,
        mock_user,
        mock_requests_get,
        mock_requests_post,
        mock_client,
        mock_get_google_provider_cfg
        ):
        """Testa o callback do login com GoogleOauth."""
        mock_get_google_provider_cfg.return_value = {
            "token_endpoint": "https://oauth2.googleapis.com/token",
            "userinfo_endpoint": "https://openidconnect.googleapis.com/v1/userinfo"
        }
        mock_requests_post.return_value.json.return_value = {
            "access_token": "mock_access_token"
        }
        mock_requests_get.return_value.json.return_value = {
            "email_verified": True,
            "sub": "mock_unique_id",
            "email": "<EMAIL>",
            "picture": "mock_picture_url",
            "hd": "pactosolucoes.com.br"
        }
        mock_client.prepare_token_request.return_value = ('','','')
        mock_client.add_token.return_value = ('','','')
        mock_user.get.return_value = None
        mock_user.create.return_value = MagicMock()

        with patch('src.api.app.auth.google_oauth') as mock_redirect:
            with patch('flask.session', {'next': '/'}):
                _response = self.client.get('/login/callback?code=mock_code')
                mock_login_user.assert_called_once()

    @patch('src.api.app.auth.firebase_auth.auth.verify_id_token')
    def test_firebase_auth_success(self, mock_verify_id_token):
        """Testa autenticação Firebase com sucesso."""
        mock_verify_id_token.return_value = {'uid': 'test_uid'}
        token = 'mock_token'
        result = handle_firebase_auth(token)
        assert result == {'uid': 'test_uid'}
        mock_verify_id_token.assert_called_once_with(token)

    @patch('src.api.app.auth.firebase_auth.auth.verify_id_token')
    def test_firebase_auth_unauthorized(self, mock_verify_id_token):
        """Testa falha na autenticação Firebase com require_auth=True."""
        mock_verify_id_token.side_effect = Exception('Invalid token')
        token = 'mock_token'
        with raises(Unauthorized):
            try:
                handle_firebase_auth(token)
                assert False, 'Should have raised Unauthorized'
            except Unauthorized as e:
                assert str(e) == 'Authentication failed'
                raise e
        mock_verify_id_token.assert_called_once_with(token)
