x-common-environments: &waitress_env
  WAITRESS_THREADS: 4
  WAITRESS_CONNECTION_LIMIT: 200
  WAITRESS_CLEANUP_INTERVAL: 10
  WAITRESS_CHANNEL_TIMEOUT: 60
  WAITRESS_BACKLOG: 1024
  WAITRESS_ASYNCORE_USE_POLL: "true"

services:

  ngrok:
    image: ngrok/ngrok:latest
    command: start --all --config=/ngrok.yml
    volumes:
      - ./ngrok.yml:/ngrok.yml
    ports:
      - 4040:4040
    extra_hosts:
      - host.docker.internal:host-gateway

  api:
    image: registry.gitlab.com/plataformazw/ia/orion/api:main
    build:
      context: .
      dockerfile: Dockerfile.api
      args:
        ARCH: x86_64
    ports:
      - "5677:5677"
      - "8300:8080"
    volumes:
      - ./src/api:/app/src/api
      - ./src/connections:/app/src/connections # Monta o diretório local ./src/connections
      - ./src/data:/app/src/data # Monta o diretório local ./src/data
      - ./src/extras:/app/src/extras # Monta o diretório local ./src/extras
      - ./src/integrations:/app/src/integrations
    environment:
      <<: *waitress_env
      SERVER_PORT: 8080
      DEBUGPY_PORT: 5677
      REDIS_URL: redis://redis:6379/0
      FLOW_NAME: Ponte
      QUESTIONARY_FLOW_NAME: Pesquisa de Satisfação / NPS
      FLASK_ENV: development # loadtesting or production or development
      FLASK_DEBUG: 'true'
      API_MASTER_KEY: 5eea8f6d-8001-4165-ad00-e1c8543b520b3e68fa15-886c-4491-b057-4b27b17df562
      AUTH_ON: 'false'
      GOOGLE_LOGIN_DOC: 'false'
      DOMAIN: "https://orion.pactosolucoes.com.br"
      RQ_QUEUE_NAME: scheduler_queue
      NOTIFICATION_EVENTS: "book_class, "
      GLOBAL_RATE_LIMIT: "60/minute"
      RATE_LIMIT_ENABLED: true
      MODE: api
      ALLOWED_ORIGINS: 'https://api.z-api.io, http://localhost:8080, ************'
      Z_API_CLIENT_TOKEN: F3b14fd459eb9462ebd51b359510e2c4dS
      RETRY_MESSAGE: 'true'
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      GCP_BIGQUERY_DATASET: development
      BUCKET_NAME_CAMPANHA: image_campanha
      SEPARATE_API_KEY: 'false'
      JAEGER_HOST: jaeger
      JAEGER_PORT: 6831

    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - jaeger

  worker:
    image: registry.gitlab.com/plataformazw/ia/orion/worker:main
    build:
      context: .
      dockerfile: Dockerfile.worker
      args:
        ARCH: x86_64
    ports:
      - "8301:8081"
      - "5678:5678"
      - "5679:5679"
      - "5686:5686"
      - "5681:5681"
    volumes:
      - ./src/worker:/app/src/worker
      - ./src/connections:/app/src/connections
      - ./src/data:/app/src/data
      - ./src/extras:/app/src/extras
      - ./src/integrations:/app/src/integrations
    # command: ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "src/worker/entrypoint.py"]
    environment:
      DEBUG: 'true'
      HOT_RELOAD: 'true'
      REDIS_URL: redis://redis:6379/0
      FLOW_NAME: Ponte
      QUESTIONARY_FLOW_NAME: Pesquisa de Satisfação / NPS
      FLASK_ENV: development # loadtesting or production or development
      NUM_THREADS: 4
      MODE: worker
      Z_API_INSTANCE_STATUS_CHECK_INTERVAL: 2
      MINUTES_FOR_CONTEXT_UPDATE: 10
      Z_API_INSTANCE_STATUS_CHECK: False # True ou False -> Em desenvolvimento, SEMPRE False
      Z_API_CLIENT_TOKEN: F3b14fd459eb9462ebd51b359510e2c4dS
      BUCKET_NAME_AUDIO: temporary_audios
      BUCKET_NAME_CAMPANHA: image_campanha
      RQ_QUEUE_NAME: scheduler_queue
      HEALTHCHECK_PORT: 8081
      PENDENCY_VERIFICATION_TIME: 60
      CHECK_PENDENCY: false
      DOMAIN: "https://orion.pactosolucoes.com.br"
      ROLES_TO_KEEP_REDIS: "assistant, user, system"
      SECONDS_TO_WAIT_TILL_RESPONSE: 5
      RETRY_CONTACT_TIME: 60
      RETRY_MESSAGE: false
      LINK_APP_TREINO: "https://apptreino.com.br/#baixar"
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      GCP_BIGQUERY_DATASET: development
      URL_PACTO_DISCOVERY_MS: http://host.docker.internal:8101  # https://discovery.ms.pactosolucoes.com.br/
      JAEGER_HOST: jaeger
      JAEGER_PORT: 6831
      BUFFER_SIZE: 10
      RUN_CONTEXT_UPDATE: false
      DELAYED_QUEUE_MAX_WORKERS: 3
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8081/health_check" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - jaeger
      - qdrant
    stdin_open: true
    tty: true
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure:5

  scheduler:
    image: registry.gitlab.com/plataformazw/ia/orion/scheduler:main
    build:
      context: .
      dockerfile: Dockerfile.scheduler
      args:
        ARCH: x86_64
    ports:
      - "8302:8084"
    volumes:
      - ./src/worker:/app/src/worker
      - ./src/connections:/app/src/connections
      - ./src/data:/app/src/data
      - ./src/extras:/app/src/extras
      - ./src/integrations:/app/src/integrations
    # command: ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "src/worker/entrypoint.py"]
    environment:
      REDIS_URL: redis://redis:6379/0
      MODE: worker # True ou False -> Em desenvolvimento, SEMPRE False
      RQ_QUEUE_NAME: scheduler_queue
      HEALTHCHECK_PORT: 8084
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      GCP_BIGQUERY_DATASET: development
    # URL_PACTO_DISCOVERY_MS: http://host.docker.internal:8101

    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8084/health_check" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - jaeger
    stdin_open: true
    tty: true

  routellm:
    image: registry.gitlab.com/plataformazw/ia/orion/routellm:main
    build:
      context: .
      dockerfile: Dockerfile.routerllm.worker
      args:
        ARCH: x86_64
    ports:
      - "8303:8082"
    volumes:
      - ./src/worker:/app/src/worker
      - ./src/connections:/app/src/connections
      - ./src/data:/app/src/data
      - ./src/extras:/app/src/extras
      - ./src/integrations:/app/src/integrations
    environment:
      REDIS_URL: redis://redis:6379/0
      FLOW_NAME: Ponte
      QUESTIONARY_FLOW_NAME: Pesquisa de Satisfação / NPS
      FLASK_ENV: development # loadtesting or production or development
      NUM_THREADS: 4
      MODE: router
      ROUTER_TYPE: mf # RouteLLM vai usar matrix factorizaton (mf) para escolher o LLM
      ROUTER_THRESHOLD: 0.35 # Reugula qual a porcentagem de menssagens vai para o LLM mais forte. 0.15609 é 10% 
      WEAKER_MODEL: GEMINI
      STRONGER_MODEL: OPEN_AI
      OPEN_AI: "openai/gpt-4o-mini"
      GEMINI: "gemini/gemini-2.0-flash"
      TOGETHER: "together_ai/togethercomputer/LLaMA-2-7B-32K"
      BUCKET_NAME_AUDIO: temporary_audios
      HEALTHCHECK_PORT: 8082
      RQ_QUEUE_NAME: scheduler_queue
      ROLES_TO_KEEP_REDIS: "assistant, user, system"
      SECONDS_TO_WAIT_TILL_RESPONSE: 5
      RETRY_CONTACT_TIME: 60
      RETRY_MESSAGE: false
      LINK_APP_TREINO: "https://apptreino.com.br/#baixar"
      GCP_BIGQUERY_DATASET: development
      JAEGER_HOST: jaeger
      JAEGER_PORT: 6831
      BUFFER_SIZE: 10

    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8082/health_check" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - jaeger
    stdin_open: true
    tty: true

  monitoring:
    image: registry.gitlab.com/plataformazw/ia/orion/monitoring:main
    build:
      context: .
      dockerfile: Dockerfile.monitoring
      args:
        ARCH: x86_64
    ports:
      - "8304:8083"
    volumes:
      - ./src/monitoring:/app/src/monitoring
      - ./src/connections:/app/src/connections
    environment:
      TZ: "America/Sao_Paulo"
      SMTP_USERNAME: "<EMAIL>"
      SMTP_PASSWORD: "a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      BIGQUERY_NOTIFICATION: false
      HEALTHCHECK_PORT: 8083
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8083/health_check" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
    stdin_open: true
    tty: true

  docs_worker:
    image: registry.gitlab.com/plataformazw/ia/orion/docs_worker:main
    build:
      context: .
      dockerfile: Dockerfile.docs_worker
      args:
        ARCH: x86_64
    ports:
      - "8305:8084"
    volumes:
      - ./src/worker/docs_worker:/app/src/worker/docs_worker
      - ./src/worker/health_check:/app/src/worker/health_check
      - ./src/connections:/app/src/connections
      - ./src/data:/app/src/data
      - ./src/extras:/app/src/extras
      - ./src/worker/llm_modules/openai/openai_embeddings_module.py:/app/src/worker/llm_modules/openai/openai_embeddings_module.py
    environment:
      TZ: "America/Sao_Paulo"
      SMTP_USERNAME: "<EMAIL>"
      SMTP_PASSWORD: "a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      HEALTHCHECK_PORT: 8083
      MODE: docs_worker
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8084/health_check" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - qdrant
    stdin_open: true
    tty: true

  redis:
    image: redis
    ports:
      - 6379:6379
    depends_on:
      - redisinsight

  locust:
    image: registry.gitlab.com/plataformazw/ia/orion/test:main
    build:
      context: .
      dockerfile: Dockerfile.test
    ports:
      - "8089:8089"

  redisinsight:
    image: redislabs/redisinsight:latest
    ports:
      - "5540:5540"

  jaeger:
    image: jaegertracing/all-in-one:1.22
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      # Remove public exposure of 16686 port - we'll expose it through nginx instead
      - "16686:16686" # On production, expose it through nginx
    environment:
      COLLECTOR_ZIPKIN_HTTP_PORT: 9411
    healthcheck:
      test: [ "CMD", "wget", "-qO-", "http://localhost:14269/" ]
      interval: 1m30s
      timeout: 30s
      retries: 5
      start_period: 30s
    networks:
      - default

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - 6333:6333
    volumes:
      #-v "$(pwd)/qdrant_storage:/qdrant/storage:z" \ -> docker run version
      - qdrant_storage:/qdrant/storage

networks:
  default:
    driver: bridge

volumes:
  qdrant_storage:
    driver: local
