stages:
  - build
  - test
  - version
  - deploy

build-api-and-test:
  stage: build
  tags:
    - shell
  retry: 2
  variables:
    TAG_API: registry.gitlab.com/plataformazw/ia/orion/api:$CI_COMMIT_REF_SLUG
    TAG_TEST: registry.gitlab.com/plataformazw/ia/orion/test:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.api -t $TAG_API .
    - docker build -f Dockerfile.test -t $TAG_TEST .
    - docker push $TAG_API
    - docker push $TAG_TEST

build-worker:
  stage: build
  tags:
    - shell
  retry: 2
  variables:
    TAG_WORKER: registry.gitlab.com/plataformazw/ia/orion/worker:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.worker -t $TAG_WORKER .
    - docker push $TAG_WORKER

build-routellm:
  stage: build
  tags:
    - shell
  retry: 2
  only:
    changes:
      - Dockerfile.routerllm.worker
      - src/routerllm/*
  variables:
    ROUTE_LLM: registry.gitlab.com/plataformazw/ia/orion/routellm:$CI_COMMIT_REF_SLUG
  script:
    - echo "Verificando alterações em Dockerfile.routerllm.worker e src/routerllm/"
    - |
      if [ "$CI_COMMIT_BEFORE_SHA" = "0000000000000000000000000000000000000000" ]; then
        echo "CI_COMMIT_BEFORE_SHA not available. Using 'HEAD' as fallback."
        # Check if there are any previous commits
        if git rev-parse HEAD~1 >/dev/null 2>&1; then
            CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
        else
            echo "This appears to be the first commit. Building all files."
            CHANGED_FILES="Dockerfile.routerllm.worker src/routerllm/"
        fi
      else
        CHANGED_FILES=$(git diff --name-only "$CI_COMMIT_BEFORE_SHA" "$CI_COMMIT_SHA")
      fi

    - echo "$CHANGED_FILES" | grep -E 'Dockerfile.routerllm.worker|src/routerllm/' || { echo "Nenhuma alteração detectada, pulando build."; exit 0; }
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.routerllm.worker -t $ROUTE_LLM .
    - docker push $ROUTE_LLM

build-scheduler:
  stage: build
  only:
    changes:
      - Dockerfile.scheduler
      - src/scheduler/*
  tags:
    - shell
  retry: 2
  variables:
    SECURITY: registry.gitlab.com/plataformazw/ia/orion/scheduler:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.scheduler -t $SECURITY .
    - docker push $SECURITY

build-docs_worker:
  stage: build
  only:
    changes:
      - Dockerfile.docs_worker
      - src/worker/docs_worker/*
      - src/data/*
  tags:
    - shell
  retry: 2
  variables:
    SECURITY: registry.gitlab.com/plataformazw/ia/orion/docs_worker:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.docs_worker -t $SECURITY .
    - docker push $SECURITY

build-monitoring:
  stage: build
  only:
    changes:
      - Dockerfile.monitoring
      - src/monitoring/*
  tags:
    - shell
  retry: 2
  variables:
    SECURITY: registry.gitlab.com/plataformazw/ia/orion/monitoring:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build --build-arg ARCH=x86_64 -f Dockerfile.monitoring -t $SECURITY .
    - docker push $SECURITY

test-merge-request-worker:
  stage: test
  tags:
    - shell
  retry: 2
  dependencies:
    - build-worker
  variables:
    WORKER_IMAGE: registry.gitlab.com/plataformazw/ia/orion/worker:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker pull $WORKER_IMAGE
    - set -o pipefail
    - docker run --rm -v $(pwd)/test_results:/app/test_results --entrypoint python $WORKER_IMAGE plo|| TESTS_FAILED=true
    - if [ "$TESTS_FAILED" = "true" ]; then echo "Testes falharam durante a execução"; exit 1; fi
    - if [ ! -f test_results/report.xml ]; then echo "Arquivo de relatório não foi gerado"; exit 1; fi
    - TEST_FAILS=$(grep 'failures=' test_results/report.xml | head -n 1 | awk -F'failures="' '{print $2}' | awk -F'"' '{print $1}' || echo "0")
    - ERRORS=$(grep 'errors=' test_results/report.xml | head -n 1 | awk -F'errors="' '{print $2}' | awk -F'"' '{print $1}' || echo "0")
    - echo "Testes falhos - $TEST_FAILS, Erros - $ERRORS"
    - if [ "${TEST_FAILS:-0}" -gt 0 ] || [ "${ERRORS:-0}" -gt 0 ]; then exit 1; else echo "Todos os testes passaram."; fi
  coverage: '/(?i)total.*? (100(?:\.0+)?|[1-9]?\d(?:\.\d+)?)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: test_results/coverage.xml
      junit: test_results/report.xml
    when: always

test-merge-request-api:
  stage: test
  tags:
    - shell
  retry: 2
  dependencies:
    - build-api-and-test
  variables:
    API_IMAGE: registry.gitlab.com/plataformazw/ia/orion/api:$CI_COMMIT_REF_SLUG
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker pull $API_IMAGE
    - set -o pipefail 
    - docker run --rm -v $(pwd)/test_results:/app/test_results --entrypoint python $API_IMAGE tests/tests.py -u -a -v -c || TESTS_FAILED=true
    - if [ "$TESTS_FAILED" = "true" ]; then echo "Testes falharam durante a execução"; exit 1; fi
    - if [ ! -f test_results/report.xml ]; then echo "Arquivo de relatório não foi gerado"; exit 1; fi
    - TEST_FAILS=$(grep 'failures=' test_results/report.xml | head -n 1 | awk -F'failures="' '{print $2}' | awk -F'"' '{print $1}' || echo "0")
    - ERRORS=$(grep 'errors=' test_results/report.xml | head -n 1 | awk -F'errors="' '{print $2}' | awk -F'"' '{print $1}' || echo "0")
    - echo "Testes falhos - $TEST_FAILS, Erros - $ERRORS"
    - if [ "${TEST_FAILS:-0}" -gt 0 ] || [ "${ERRORS:-0}" -gt 0 ]; then exit 1; else echo "Todos os testes passaram."; fi
  coverage: '/(?i)total.*? (100(?:\.0+)?|[1-9]?\d(?:\.\d+)?)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: test_results/coverage.xml
      junit: test_results/report.xml
    when: always

version:
  image: registry.gitlab.com/plataformazw/tag-versions:0.6.0
  tags:
    - docker
  stage: version
  when: on_success
  retry: 2
  interruptible: true
  except:
    changes:
      - .bumpversion.cfg
  only:
    - main
  script:
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - |
      case "$CI_COMMIT_REF_NAME" in
        hotfix/*)
          echo "Detectado branch hotfix/*, aplicando bumpversion patch"
          bumpversion patch
          ;;
        feature/*)
          echo "Detectado branch feature/*, aplicando bumpversion minor"
          bumpversion minor
          ;;
        release/*)
          echo "Detectado branch release/*, aplicando bumpversion major"
          bumpversion major
          ;;
        *)
          echo "Nenhum padrão de branch reconhecido, aplicando bumpversion patch como padrão"
          bumpversion patch
          ;;
      esac
    - git remote set-url --push origin https://gitlab-ci-token:${GITLAB_PIPE}@gitlab.com/$CI_PROJECT_PATH.git
    - git tag -f latest -m "Latest tag"
    - git push origin HEAD:$CI_COMMIT_REF_NAME -o ci.skip
    - git push -f --tags


deploy-to-kubernetes:
  stage: deploy
  image: google/cloud-sdk:latest
  tags:
    - docker
  retry: 2
  dependencies:
    - version
  except:
    - tags/latest
  only:
    refs:
      - tags
    variables:
      - $CI_COMMIT_TAG =~ /^v.*/
  script:
    - echo "Deploying to Kubernetes cluster - conversas-ai-cluster"
    - echo "$GCP_SERVICE_ACCOUNT_KEY"
    - echo "$GCP_SERVICE_ACCOUNT_KEY" | base64 -d > $HOME/gcp-key.json
    - gcloud auth activate-service-account --key-file=$HOME/gcp-key.json
    - gcloud container clusters get-credentials conversas-ai-cluster --region southamerica-east1 --project conversas-ai
    - echo "Deploy applications"
    - kubectl set image deployment/conversas-ai-api api=registry.gitlab.com/plataformazw/ia/orion/api:$CI_COMMIT_REF_SLUG -n conversas-ai
    - kubectl set image deployment/conversas-ai-worker worker=registry.gitlab.com/plataformazw/ia/orion/worker:$CI_COMMIT_REF_SLUG -n conversas-ai
    - kubectl set image deployment/conversas-ai-docs-worker docs-worker=registry.gitlab.com/plataformazw/ia/orion/docs_worker:$CI_COMMIT_REF_SLUG -n conversas-ai
    - kubectl set image deployment/conversas-ai-scheduler scheduler=registry.gitlab.com/plataformazw/ia/orion/scheduler:$CI_COMMIT_REF_SLUG -n conversas-ai
    - echo "Check deployments"
    - kubectl rollout status deployment/conversas-ai-api -n conversas-ai
    - kubectl rollout status deployment/conversas-ai-worker -n conversas-ai
    - kubectl rollout status deployment/conversas-ai-docs-worker -n conversas-ai
    - kubectl rollout status deployment/conversas-ai-scheduler -n conversas-ai
    - echo "Deployment completed successfully"
  environment:
    name: Production
    url: https://api.conversas.ai