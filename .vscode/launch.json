{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Delete context", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "args": ["--api-key", "02b8b518d0586242df0da36893c6a03e6808aa6b5deeb3814b08b647de661381", "--empresa", "a4eaccb9b154a6c749240f8235677ba9-5", "--telefone", "+5562996026753", "--env", "prod", "--no-auth", "false", "--rede", "true"]}, {"name": "Attach to Api", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5677}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to messages_received_worker", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 1", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5679}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 2", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5680}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 3", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5681}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 4", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5682}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 5", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5683}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 6", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5684}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 7", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5685}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to DelayedQueueWorker", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5686}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 9", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5687}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Attach to Worker 10", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5688}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}], "compounds": [{"name": "Debug All Workers", "configurations": ["Attach to Worker 0", "Attach to Worker 1", "Attach to Worker 2", "Attach to Worker 3", "Attach to Worker 4", "Attach to Worker 5", "Attach to Worker 6", "Attach to Worker 7", "Attach to Worker 8", "Attach to Worker 9", "Attach to Worker 10"]}]}