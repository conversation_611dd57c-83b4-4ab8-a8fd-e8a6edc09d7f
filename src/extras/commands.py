"""Módulo para regras de negócio dos comandos de teste permitidos"""

import json
import os
from enum import Enum

from src.connections.connections import Connections

import logging

__all__ = ["Command", "parse_command"]

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")

CONN = Connections.get_instance()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")



class Command(Enum):
    FINISH = "A conversa será finalizada!"
    DONE = "Operação realizada com sucesso!"
    PING = "pong"
    NOOP = "NOOP"


def parse_command(
    message: str, telefone: str, id_empresa: str, for_matriz: bool = False
) -> Command:
    print(f"\n\n\n\n{message}\n\n\n\n")
    match message.strip().lower():
        case "#apagar_contexto": return apagar_contexto(
            telefone, id_empresa, for_matriz
        )
        case "#fim": return fim()
        case "#ping": return Command.PING
        case _: return Command.NOOP


def fim() -> Command:
    return Command.FINISH


def apagar_contexto(
    telefone: str, id_empresa: str, for_matriz: bool = False
) -> Command:
    redis_client = CONN.redis_client
    bq_client = CONN.bigquery_client

    col = "id_empresa" if not for_matriz else "id_matriz"

    query = (f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_usuario "
             f"WHERE telefone = '{telefone}' AND {col} = '{id_empresa}'")

    logger.info(f"Executando query para apagar contexto:\n\t{query}")

    result = bq_client.query(query).result()

    redis_client.delete(f"{telefone}-{id_empresa}")
    redis_client.delete(f"{telefone}-waiting_cpf")
    redis_client.delete(f"is_client:{id_empresa}:{telefone}")
    redis_client.delete(f"system_context:{id_empresa}:{telefone}")
    redis_client.delete(f"save_empresa:{id_empresa}-{telefone}")
    redis_client.delete(f"retry_contact:{telefone}-{id_empresa}")
    redis_client.delete(f"pendency_verification:{telefone}-{id_empresa}")
    redis_client.delete(f"current_conversation:{telefone}-{id_empresa}")
    redis_client.delete(f"id_empresa-{telefone}-{id_empresa}")
    redis_client.delete(f"CPF_search:{id_empresa}:{telefone}")
    redis_client.delete(f"memories:{id_empresa}:{telefone}")
    message_placeholder = [
        {
            "enviado_por": "assistant",
            "mensagem": "...",
        }
    ]
    redis_client.delete(f"last_messages-{telefone}-{id_empresa}")
    redis_client.set(
        f"last_messages-{telefone}-{id_empresa}",
        json.dumps(message_placeholder),
        ex=24*60*60
    )
    return Command.DONE
