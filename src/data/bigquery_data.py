import os
import json
import logging
import urllib.parse
import uuid
import requests
import ast
from typing import Literal
from time import sleep

from pandas import DataFrame, Timestamp, concat
from google.cloud import bigquery
import re
import base64
import urllib
from datetime import datetime

from src.connections.connections import Connections
from src.extras.util import retry, parse_phone, get_date, get_name_matricula_context, log_func_time
from src.integrations.pacto.default_data.pacto_default_data import PactoDefaultData as pdc
from src.data.google_storage import Bucket

GCP_BIGQUERY_PROJECT_ID = os.getenv("GCP_BIGQUERY_PROJECT_ID")
GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if GCP_BIGQUERY_DATASET == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

connections = Connections.get_instance()
logger = logging.getLogger("conversas_logger")

@retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
def get_from_empresa(id_empresa):
    cache_instance_id = redis_client_get(f"instances:{id_empresa}")
    if cache_instance_id:
        result_data = json.loads(cache_instance_id)
    else:
        query = f"SELECT instance_id, token FROM {GCP_BIGQUERY_DATASET}.instances WHERE id_empresa = '{id_empresa}'"
        result_data = connections.bigquery_client.query(query).result().to_dataframe()
        result_data = json.loads(result_data.to_json(orient='records'))
        result_data = result_data[0] if result_data else None
    if not result_data:
        return None, None
    redis_client_set(
        f"instances:{id_empresa}",
        json.dumps(result_data),
        ex=8*60*60
    )
    instance_id, token = result_data['instance_id'], result_data['token']
    return instance_id, token

@retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
def get_from_instance(instance_id):
    cache_instance_id = redis_client_get(f"empresas:{instance_id}")
    if cache_instance_id:
        result_data = json.loads(cache_instance_id)
    else:
        query = f"SELECT id_empresa, token FROM {GCP_BIGQUERY_DATASET}.instances WHERE instance_id = '{instance_id}'"
        result_data = connections.bigquery_client.query(query).result().to_dataframe()
        result_data = json.loads(result_data.to_json(orient='records'))
        result_data = result_data[0] if result_data else None
    if not result_data:
        return None, None
    redis_client_set(
        f"empresas:{instance_id}",
        json.dumps(result_data),
        ex=8*60*60
    )
    id_empresa, token = result_data['id_empresa'], result_data['token']
    return id_empresa, token


def get_id_empresa():
    try:
        cache_id_empresa = redis_client_get(
            "id_empresas_atualizaram_contexto:")
        if cache_id_empresa:
            logger.info("Obtendo id_empresa do cache...")
            return json.loads(cache_id_empresa)

        logger.info("Obtendo id_empresa do BigQuery...")
        query = f"SELECT id_empresa FROM `{GCP_BIGQUERY_DATASET}.instances` where instance_id is not null"
        result = connections.bigquery_client.query(query).result().to_dataframe()

        if result.empty:
            return []

        lista_empresas = result["id_empresa"].dropna().tolist()
        ex = int(os.getenv("MINUTES_FOR_CONTEXT_UPDATE", "10")) * 10
        redis_client_set("id_empresas_atualizaram_contexto:", json.dumps(lista_empresas), ex=ex) 
        return lista_empresas

    except Exception as e:
        logger.error(f"Erro ao buscar id_empresa: {e}")
        return []
        

@ retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
def is_rede(instance_id):
    cache_data = redis_client_get(f"is_rede:{instance_id}")
    if cache_data:
        result_data = json.loads(cache_data)
    else:
        result_data = connections.bigquery_client.query(f"SELECT is_rede FROM {GCP_BIGQUERY_DATASET}.instances WHERE instance_id = '{instance_id}'").result().to_dataframe() 
        result_data = json.loads(result_data.to_json(orient='records'))
        result_data = result_data[0] if result_data else None
    if not result_data:
        return None
    redis_client_set(
        f"is_rede:{instance_id}",
        json.dumps(result_data),
        ex=8*60*60
    )
    is_rede = result_data['is_rede']
    return is_rede

@retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
def get_from_telefone(telefone) -> str | None:
    cache_telefone = redis_client_get(f"empresas_telefone:{telefone}")
    if cache_telefone:
        result_data = cache_telefone.decode('utf-8')
        return result_data

    query = f"SELECT id_empresa FROM {GCP_BIGQUERY_DATASET}.contexto_academia WHERE telefone = '{telefone}'"
    result_data = connections.bigquery_client.query(query).result().to_dataframe()
    result_data = json.loads(result_data.to_json(orient='records'))
    result_data = result_data[0] if result_data else None

    if not result_data:
        return None

    id_empresa = result_data['id_empresa']
    redis_client_set(
        f"empresas_telefone:{telefone}",
        id_empresa,
        ex=8*60*60
    )
    return id_empresa

def check_if_key_exists(key):
    try:
        return connections.redis_client.exists(key)
    except Exception as e:
        logger.error(f"Error checking if key {key} exists: {e}")
        return False

def redis_client_get(key):
    try:
        return connections.redis_client.get(key)
    except Exception as e:
        logger.error(f"Error getting key {key} from Redis: {e}")
        return None
    
def redis_client_set(key, value, ex=None):
    try:
        connections.redis_client.set(key, value, ex=ex)
    except Exception as e:
        logger.error(f"Error setting key {key} in Redis: {e}")


class BigQueryData:
    def __init__(self, id_empresa, id_matriz=None):
        self.id_empresa = id_empresa
        self.id_matriz = id_matriz

    def set_id_empresa(self, id_empresa):
        self.id_empresa = id_empresa

    def _get_client(self):
        return connections.bigquery_client

    @log_func_time
    @retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
    def _execute_query(self, query, extra_query="", use_id_empresa=True):
        if use_id_empresa:
            query = query + f" AND id_empresa = '{self.id_empresa}'" if "WHERE" in query else query + f" WHERE id_empresa = '{self.id_empresa}'"
        query += extra_query
        client = self._get_client()
        logger.info(f"Executando query:\n{query}")
        query_job = client.query(query)
        result = query_job.result()
        return result
    
    @log_func_time
    def _execute_query_no_error(self, query, extra_query="", use_id_empresa=True):
        try:
            if use_id_empresa:
                query = query + f" AND id_empresa = '{self.id_empresa}'" if "WHERE" in query else query + f" WHERE id_empresa = '{self.id_empresa}'"
            query += extra_query
            client = self._get_client()
            logger.info(f"Executando query:\n{query}")
            query_job = client.query(query)
            result = query_job.result()
            return result
        except Exception as e:
            logger.error(f"Erro ao executar query:\n{query}\nErro: {e}")
            return None
        
    @log_func_time
    @retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
    def _check_if_row_exists(
        self, table_id, id_column,
        id_value, column_type="string", use_id_empresa=False,
        use_id_matriz=False, extra_query=""
    ):
        try:
            query = f"SELECT {id_column} FROM {table_id} WHERE {id_column}" + (f" = '{id_value}'" if column_type == "string" else f" = {id_value}")
            if extra_query != "":
                query += extra_query
            result = self._execute_query(
                query,
                extra_query=f" AND id_matriz = '{self.id_matriz}'" if use_id_matriz else "",
                use_id_empresa=use_id_empresa
            )
            logger.info(f"\nTotal de linhas encontradas: {result.total_rows}\n")
            return result.total_rows > 0
        except Exception as e:
            logger.error(f"Erro ao verificar se linha existe no BigQuery: {e}")
            return False

    @log_func_time
    @retry(retries=3, delay=2, backoff=2, status_codes=(500,404,401,402), exceptions=(requests.exceptions.RequestException, TypeError, Exception))
    def _save_dataframe_to_bq(self, query, job_config=None):
        client = self._get_client()
        logger.info(f"Salvando dados no BigQuery: {query}")
        result = client.query(query, job_config=job_config).result()
        return result

    @log_func_time
    def create_empresa(self, data):
        try:
            logger.info("Criando empresa...")
            table_id = f"{GCP_BIGQUERY_DATASET}.empresas"
            dados_json = json.dumps(data, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            query = f"""
                INSERT INTO `{table_id}` (contexto_empresa_json, data_ultima_atualizacao, id_empresa)
                VALUES (@contexto_empresa_json, @data_ultima_atualizacao, @id_empresa)
            """
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("contexto_empresa_json", "JSON", dados_json),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                ]
            )
            self._save_dataframe_to_bq(query, job_config=job_config)
            redis_client_set(
                f"empresas:{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao criar empresa: {e}")

    @log_func_time
    def update_empresa(self, data):
        try:
            logger.info("Atualizando empresa...")
            table_id = f"{GCP_BIGQUERY_DATASET}.empresas"
            dados_json = json.dumps(data, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            query = f"""
                UPDATE `{table_id}`
                SET contexto_empresa_json = @contexto_empresa_json,
                    data_ultima_atualizacao = @data_ultima_atualizacao
                WHERE id_empresa = @id_empresa
            """
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("contexto_empresa_json", "JSON", dados_json),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                ]
            )
            self._save_dataframe_to_bq(query, job_config=job_config)
            redis_client_set(
                f"empresas:{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa: {e}")

    @log_func_time
    def patch_empresa(self, data):
        try:
            current_data = self.get_empresa()
            for key in data.keys():
                if key in current_data:
                    current_data[key] = data[key]

            self.update_empresa(current_data)
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa: {e}")

    @log_func_time
    def get_empresa(self):
        try:
            cache_empresa = redis_client_get(f"empresas:{self.id_empresa}")
            if cache_empresa:
                logger.info("Obtendo empresa do cache...")
                return json.loads(cache_empresa)
            logger.info("Obtendo empresa...")
            result = self._execute_query(f"SELECT TO_JSON_STRING(contexto_empresa_json) FROM {GCP_BIGQUERY_DATASET}.empresas")
            result = result.to_dataframe()
            if result.empty:
                return {}
            empresa = result.iloc[0]['contexto_empresa_json']
            return json.loads(empresa)
        except Exception as e:
            logger.error(f"Erro ao obter empresa: {e}")
            return {}
               
    @log_func_time
    def delete_empresa(self):
        try:
            logger.info("Deletando empresa...")
            table_id = f"{GCP_BIGQUERY_DATASET}.empresas"
            query = f"DELETE FROM {table_id} WHERE id_empresa = '{self.id_empresa}'"
            self._execute_query(query)
            connections.redis_client.delete(f"empresas:{self.id_empresa}")
        except Exception as e:
            logger.error(f"Erro ao deletar empresa: {e}")

    @log_func_time
    def create_sticker(self, data: dict):
        """Cria um sticker no BigQuery e no Redis."""
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.stickers"
            if not self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                redis_client_set(
                    f"sticker-{self.id_empresa}",
                    data.get("sticker"),
                    ex=8*60*60
                )
                logger.info("Criando sticker...")
                logger.info("Data: %s", data)
                data_atualizacao = Timestamp.now()
                query = f"""
                    INSERT INTO `{table_id}` (sticker, data_ultima_atualizacao, id_empresa)
                    VALUES (@sticker, @data_ultima_atualizacao, @id_empresa)
                """
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter(
                            "sticker",
                            "STRING",
                            str(data.get("sticker"))
                            ),
                        bigquery.ScalarQueryParameter(
                            "data_ultima_atualizacao",
                            "TIMESTAMP",
                            data_atualizacao
                            ),
                        bigquery.ScalarQueryParameter(
                            "id_empresa",
                            "STRING",
                            self.id_empresa
                            ),
                    ]
                )
                self._save_dataframe_to_bq(query, job_config=job_config)
        except Exception as e:
            logger.error("Erro ao criar sticker: %s", e)

    @log_func_time
    def update_sticker(self, data):
        """Atualiza um sticker no BigQuery e no Redis."""
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.stickers"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                redis_client_set(
                    f"sticker-{self.id_empresa}",
                    data.get("sticker"),
                    ex=8*60*60
                )
                logger.info("Atualizando sticker...")
                data_atualizacao = Timestamp.now()
                query = f"""
                    UPDATE `{table_id}`
                    SET sticker = @sticker,
                        data_ultima_atualizacao = @data_ultima_atualizacao
                    WHERE id_empresa = @id_empresa
                """
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter(
                            "sticker",
                            "STRING",
                            str(data.get("sticker"))
                            ),
                        bigquery.ScalarQueryParameter(
                            "data_ultima_atualizacao",
                            "TIMESTAMP",
                            data_atualizacao
                            ),
                        bigquery.ScalarQueryParameter(
                            "id_empresa",
                            "STRING",
                            self.id_empresa
                            ),
                    ]
                )
                self._save_dataframe_to_bq(query, job_config=job_config)
        except Exception as e:
            logger.error("Erro ao atualizar sticker: %s", e)

    @log_func_time
    def get_sticker(self) -> str:
        """Obtém um sticker do BigQuery ou do Redis."""
        try:
            if check_if_key_exists(f"sticker-{self.id_empresa}"):
                return redis_client_get(f"sticker-{self.id_empresa}")
            logger.info("Obtendo sticker...")
            result = self._execute_query(f"SELECT sticker FROM {GCP_BIGQUERY_DATASET}.stickers")
            result = result.to_dataframe()
            if result.empty:
                return None
            return result.iloc[0]['sticker']
        except Exception as e:
            logger.error("Erro ao obter sticker: %s", e)
            return None

    @log_func_time
    def delete_sticker(self):
        """Deleta um sticker do BigQuery e do Redis."""
        try:
            if check_if_key_exists(f"sticker-{self.id_empresa}"):
                connections.redis_client.delete(f"sticker-{self.id_empresa}")
            logger.info("Deletando sticker...")
            table_id = f"{GCP_BIGQUERY_DATASET}.stickers"
            query = f"DELETE FROM {table_id}"
            self._execute_query(query)
        except Exception as e:
            logger.error("Erro ao deletar sticker: %s", e)

    @log_func_time
    def get_gym_context(self):
        try:
            cache_gym_context = redis_client_get(f"gym_context-{self.id_empresa}")
            if cache_gym_context:
                logger.info("Obtendo contexto da academia do cache...")
                return json.loads(cache_gym_context)
            
            logger.info("Obtendo contexto da academia do BigQuery...")
            result = self._execute_query(f"SELECT contexto_academia_json FROM {GCP_BIGQUERY_DATASET}.contexto_academia")
            gym_context = result.to_dataframe().iloc[0]['contexto_academia_json']
            gym_context_json = json.loads(gym_context)
            redis_client_set(
                f"gym_context-{self.id_empresa}",
                gym_context,
                ex=8*60*60
            )
            
            return gym_context_json
        except Exception as e:
            logger.error(f"Erro ao obter contexto da academia: {e}")
            return {}

    @log_func_time
    def update_gym_context(self, context):
        try:
            logger.info("Atualizando contexto da academia...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_academia"
            dados_json = json.dumps(context, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            id_empresa = self.id_empresa

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"""
                    UPDATE `{table_id}`
                    SET contexto_academia_json = @contexto_academia_json,
                        data_ultima_atualizacao = @data_ultima_atualizacao
                    WHERE id_empresa = @id_empresa
                """
            else:
                query = f"""
                    INSERT INTO `{table_id}` (contexto_academia_json, data_ultima_atualizacao, id_empresa)
                    VALUES (@contexto_academia_json, @data_ultima_atualizacao, @id_empresa)
                """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("contexto_academia_json", "STRING", dados_json),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", id_empresa),
                ]
            )

            self._save_dataframe_to_bq(query, job_config=job_config)
            redis_client_set(
                f"gym_context-{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )

        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da academia: {e}")

    @log_func_time
    def get_plans_context(self):
        try:
            cache_plans_context = redis_client_get(f"plans_context-{self.id_empresa}")
            if cache_plans_context:
                logger.info("Obtendo contexto dos planos do cache...")
                return json.loads(cache_plans_context)
            
            logger.info("Obtendo contexto dos planos do BigQuery...")
            result = self._execute_query(f"SELECT * FROM {GCP_BIGQUERY_DATASET}.contexto_planos")
            result_data = result.to_dataframe()
            result_data_json = result_data['planos_json'].iloc[0]
            redis_client_set(
                f"plans_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )
            
            return json.loads(result_data_json)
        except Exception as e:
            logger.error(f"Erro ao obter contexto dos planos: {e}")
            return None

    @log_func_time
    def update_plans_context(self, planos):
        try:
            logger.info("Atualizando contexto dos planos...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_planos"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET planos_json = '{json.dumps(planos)}', data_ultima_atualizacao = '{Timestamp.now()}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (planos_json, data_ultima_atualizacao, id_empresa) VALUES ('{json.dumps(planos)}', '{Timestamp.now()}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)
            # Save to cache
            redis_client_set(
                f"plans_context-{self.id_empresa}",
                json.dumps(planos),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto dos planos: {e}")

    @log_func_time
    def get_phase_context(self, phase_name=None, get_all=False):
        try:
            cache_key = f"phases_context-{self.id_empresa}"
            cache_phases_context = redis_client_get(cache_key)
            if cache_phases_context:
                logger.info("Obtendo contexto das fases do cache...")
                phases_context = json.loads(cache_phases_context)
                if get_all:
                    return phases_context
                if not phase_name and len(phases_context) > 0:
                    return phases_context[0]
                for phase in phases_context:
                    if phase['nome_fase'] == phase_name:
                        return phase
                    
            logger.info("Obtendo contexto das fases do BigQuery...")
            query = f"SELECT * FROM {GCP_BIGQUERY_DATASET}.contexto_fases"
            if phase_name:
                query += f" WHERE nome_fase = '{phase_name}'"
            extra_query = '' if get_all else ' LIMIT 1'
            result = self._execute_query(query, extra_query=extra_query)
            result_data = result.to_dataframe()
            result_data_json = json.loads(result_data.to_json(orient='records'))
            phase = result_data_json[0] if result_data_json else getattr(pdc.crm_phase, phase_name) if phase_name else pdc.crm_phase.LEADS_HOJE
            redis_client_set(
                cache_key,
                json.dumps(result_data_json),
                ex=8*60*60
            )
            if get_all:
                return result_data_json
            else:
                return phase
        except Exception as e:
            logger.error(f"Erro ao obter contexto das fases: {e}")
            return pdc.crm_phase.LEADS_HOJE

    @log_func_time
    def update_phases_context(self, context):
        try:
            logger.info("Atualizando contexto das fases...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_fases"
            df = DataFrame({
                "codigo_fase": [fase.get('codigo', -1) for fase in context],
                "nome_fase": [fase['name'] for fase in context],
                "descricao_fase": [fase['descricao'] for fase in context],
                "instrucao_ia_fase": [fase['instrucao_ia'] for fase in context],
                "data_ultima_atualizacao": Timestamp.now(),
                "id_empresa": [self.id_empresa for _ in context]
            })
            for fase in context:
                if self._check_if_row_exists(table_id, "nome_fase", fase['name'], use_id_empresa=True):
                    query = f"""
                    UPDATE {table_id} 
                    SET 
                    codigo_fase = {int(fase.get('codigo', -1))},
                    nome_fase = '{fase['name']}',
                    descricao_fase = '''{fase['descricao']}''',
                    instrucao_ia_fase = '''{fase['instrucao_ia']}''',
                    data_ultima_atualizacao = '{Timestamp.now()}'
                    WHERE nome_fase = '{fase['name']}' and id_empresa = '{self.id_empresa}'"""
                    self._save_dataframe_to_bq(query=query)
                else:
                    query = f"INSERT INTO {table_id} (codigo_fase, nome_fase, descricao_fase, instrucao_ia_fase, data_ultima_atualizacao, id_empresa) VALUES ({int(fase.get('codigo', -1))}, '{fase['name']}', '''{fase['descricao']}''', '''{fase['instrucao_ia']}''', '{Timestamp.now()}', '{self.id_empresa}')"
                    self._save_dataframe_to_bq(query=query)
            phases_context = redis_client_get(f"phases_context-{self.id_empresa}")
            
            if phases_context:
                phases_context = json.loads(phases_context)
                df = concat([DataFrame(phases_context), df]).drop_duplicates(subset=['codigo_fase'], keep='last')

            redis_client_set(
                f"phases_context-{self.id_empresa}",
                json.loads(json.dumps(df.to_json(orient='records'))),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto das fases: {e}")

    @log_func_time
    def get_classes_context(self):
        try:
            cache_classes_context = redis_client_get(f"classes_context-{self.id_empresa}")
            if cache_classes_context:
                logger.info("Obtendo contexto das turmas do cache...")
                return json.loads(cache_classes_context)
            
            logger.info("Obtendo contexto das turmas do BigQuery...")
            result = self._execute_query(f"SELECT * FROM {GCP_BIGQUERY_DATASET}.contexto_turmas")
            result_data = result.to_dataframe()
            result_data_json = result_data['turmas'].iloc[0]
            redis_client_set(
                f"classes_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )

            return result_data_json
        except Exception as e:
            logger.error(f"Erro ao obter contexto das turmas: {e}")
            return None
        
    @log_func_time
    def update_classes_context(self, context):
        try:
            logger.info("Atualizando contexto das turmas...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_turmas"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET turmas = '{json.dumps(context)}', data_ultima_atualizacao = '{Timestamp.now()}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (turmas, data_ultima_atualizacao, id_empresa) VALUES ('{json.dumps(context)}', '{Timestamp.now()}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)
            # Save to cache
            redis_client_set(
                f"classes_context-{self.id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto das turmas: {e}")

    @log_func_time
    def get_products_context(self):
        try:
            cache_products_context = redis_client_get(f"products_context-{self.id_empresa}")
            if cache_products_context:
                logger.info("Obtendo contexto dos produtos do cache...")
                if isinstance(cache_products_context, bytes):
                    cache_products_context = cache_products_context.decode('utf-8')
                try:
                    return json.loads(cache_products_context)
                except json.JSONDecodeError:
                    return ast.literal_eval(cache_products_context)
            
            logger.info("Obtendo contexto dos produtos do BigQuery...")
            result = self._execute_query(f"SELECT * FROM {GCP_BIGQUERY_DATASET}.contexto_produtos")
            result_data = result.to_dataframe()
            result_data_json = result_data['produtos'].iloc[0]  
            

            redis_client_set(
                f"products_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )

            try:
                return json.loads(result_data_json) 
            except json.JSONDecodeError:
                return ast.literal_eval(result_data_json)
            
        except Exception as e:
            logger.error(f"Erro ao obter contexto dos produtos: {e}")
            return None
        
    @log_func_time
    def update_products_context(self, context):
        try:
            logger.info("Atualizando contexto dos produtos...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_produtos"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET produtos = '''{context}''', data_ultima_atualizacao = '{Timestamp.now()}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (produtos, data_ultima_atualizacao, id_empresa) VALUES ('''{context}''', '{Timestamp.now()}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)
            redis_client_set(
                f"products_context-{self.id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto dos produtos: {e}")

    @log_func_time
    def get_user_context(self, telefone, use_id_matriz=False, use_id_empresa=True) -> tuple[dict, str]:
        telefone = str(telefone)
        logger.info(f"[GET_USER_CONTEXT] Obtendo contexto do usuário para o telefone: {telefone} (use_id_empresa={use_id_empresa}, use_id_matriz={use_id_matriz})")
        try:
            if use_id_empresa:
                cache_user_context = redis_client_get(f"{telefone}-{self.id_empresa}")
            else:
                cache_user_context = redis_client_get(f"{telefone}-{self.id_matriz}")

            logger.info(f"Tem cache? {cache_user_context is not None}")

            if self.id_empresa and cache_user_context:
                logger.info("Obtendo contexto do usuário do cache...")
                cache_user_context = json.loads(cache_user_context)
                nome, matricula = get_name_matricula_context(cache_user_context)
                logger.info(f"[QUEM][GET][REDIS] Nome: {nome}, Matricula: {matricula}")
                cache_user_context.pop('origin_last_update', None) 

                # TODO: consertar isso
                fase_atual = cache_user_context.get("fase_crm") or cache_user_context.get("aluno", {}).get("fase_crm") or cache_user_context.get("fase_atual") or "LEADS_HOJE"
                if not fase_atual:
                    return cache_user_context, None
                return cache_user_context, fase_atual

            logger.info("Obtendo contexto do usuário do BigQuery...")
            query = f"SELECT * FROM {GCP_BIGQUERY_DATASET}.contexto_usuario WHERE telefone = '{telefone}'"
            result = self._execute_query(
                query=query, 
                extra_query= f" AND id_matriz = '{self.id_matriz}'" if use_id_matriz else "",
                use_id_empresa=use_id_empresa
            )
            result_data = result.to_dataframe()

            if result_data.empty:
                return None, None
            result_data_row = result_data.iloc[0]

            result_data_json = json.loads(result_data_row['contexto_usuario_json'])
            nome, matricula = get_name_matricula_context(result_data_json)
            logger.info(f"[QUEM][GET][BQ] Nome: {nome}, Matricula: {matricula}")
            # TODO: consertar isso
            fase_atual = result_data_json.get("fase_crm") or result_data_json.get("aluno", {}).get("fase_crm") or result_data_json.get("aluno", {}).get("fase_atual") or "LEADS_HOJE"
            result_data_json['fase_atual'] = fase_atual
            result_data_json = {
                'origin_last_update': result_data_row['origin_last_update'],
                **result_data_json
            }
            
            if use_id_empresa:
                redis_client_set(
                    f"{telefone}-{self.id_empresa}",
                    json.dumps(result_data_json),
                    ex=8*60*60
                )
            else:
                redis_client_set(
                    f"{telefone}-{self.id_matriz}",
                    json.dumps(result_data_json),
                    ex=8*60*60
                )

            result_data_json.pop('origin_last_update', None) 
            return result_data_json, fase_atual
        except Exception as e:
            logger.error(f"Erro ao obter contexto do usuário: {e}")
            return None, None

    @log_func_time
    def get_id_empresa_from_matriz(self, telefone):
        try:
            cache = redis_client_get(f"save_empresa:{self.id_matriz}-{telefone}")
            
            if cache:
                return cache.decode('utf-8')
            query = f"SELECT id_empresa FROM {GCP_BIGQUERY_DATASET}.contexto_usuario WHERE id_matriz = '{self.id_matriz}' AND telefone = '{telefone}'"
            result = self._execute_query(query, use_id_empresa=False)
            result_data = result.to_dataframe()
            if result_data.empty:
                return None
            id_empresa = result_data.iloc[0]['id_empresa']
            if id_empresa is None or id_empresa == 'None':
                return None
            redis_client_set(f"save_empresa:{self.id_matriz}-{telefone}", result_data.iloc[0]['id_empresa'])
            return result_data.iloc[0]['id_empresa']
        except Exception as e:
            logger.error(f"Erro ao obter id_empresa da matriz: {e}")
            return None

    @log_func_time
    def save_user_context(self, context, telefone, fase, origin=None):
        logger.info(f"[SET_USER_CONTEXT] Salvando contexto do usuário para o telefone: {telefone}, fase: {fase}")

        if context == {} or not context:
            return
        telefone = str(telefone)
        # add phase to context
        if isinstance(context, str):
            context = json.loads(context)
        logger.info(f"type(context): {type(context)}")
        context['fase_atual'] = fase
        nome, matricula = get_name_matricula_context(context)
        logger.info(f"[QUEM][SAVE][BQ] Nome: {nome}, Matricula: {matricula}")
        context = json.dumps(context).encode('utf-8').decode('utf-8')
        try:
            logger.info("Salvando contexto do usuário...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_usuario"
            # use_id_matriz=True porque se não for uma rede, o id_empresa é o mesmo que o id_matriz
            id_usuario_unico = f"{telefone}-{self.id_matriz}" if self.id_matriz else f"{telefone}-{self.id_empresa}"
            logger.info(f"id_usuario_unico? {id_usuario_unico}")
            if self._check_if_row_exists(table_id, "id_usuario_unico", id_usuario_unico):
                query = f"UPDATE {table_id} SET id_usuario_unico = '{id_usuario_unico}', contexto_usuario_json = '{context}', data_ultima_atualizacao = '{Timestamp.now()}', fase_atual = '{fase}', origin_last_update = '{str(origin)}' WHERE telefone = '{telefone}' AND id_matriz = '{self.id_matriz}'"
            else:
                query = f"INSERT INTO {table_id} (id_usuario_unico, id_usuario, contexto_usuario_json, data_ultima_atualizacao, telefone, id_empresa, fase_atual, id_matriz, origin_last_update) VALUES ('{id_usuario_unico}', '', '{context}', '{Timestamp.now()}', '{telefone}', '{self.id_empresa}', '{fase}', '{self.id_matriz}', '{str(origin)}')"
            self._save_dataframe_to_bq(query=query)
            # Save to cache
            logger.info(f"[QUEM][SAVE][REDIS] id_empresa: {self.id_empresa}. Salvando contexto do usuário no Redis: {telefone}-{self.id_empresa}")
            if self.id_empresa:
                context = json.loads(context) if isinstance(context, str) else context
                context = {
                    'origin_last_update': origin,
                    **context
                }
                context = json.dumps(context).encode('utf-8').decode('utf-8')
                redis_client_set(
                    f"{telefone}-{self.id_empresa}",
                    context,
                    ex=8*60*60
                )
        except Exception as e:
            logger.error(f"Erro ao salvar contexto do usuário: {e}")

    @log_func_time
    def save_user_origin(self, origin : str, telefone: str, id_empresa: str):
        """Salva a origem do lead no BigQuery"""
        
        try:
            logger.info("Salvando origem do usuário...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_usuario"

            query = (
                f"UPDATE {table_id} "
                f"SET origin = '{origin}', data_ultima_atualizacao = '{Timestamp.now()}' "
                f"WHERE telefone = '{telefone}' "
            )
            self._execute_query(query)
        except Exception as e:
            logger.error(f"Erro ao salvar a origem do lead: {e}")

    @log_func_time
    def save_user_empresa(self, id_empresa, context, telefone, fase):
        telefone = str(telefone)
        # add phase to context
        try:
            logger.info("Salvando empresa do usuário...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_usuario"
            logger.info(f"Já existe?")
            if self._check_if_row_exists(table_id, "telefone", telefone, use_id_matriz=True):
                query = f"UPDATE {table_id} SET id_usuario_unico = '{telefone+'-'+str(id_empresa)}', id_empresa = '{id_empresa}', data_ultima_atualizacao = '{Timestamp.now()}', fase_atual = '{fase}', contexto_usuario_json = '''{context}''' WHERE telefone = '{telefone}' AND id_matriz = '{self.id_matriz}'"
            else:
                query = f"INSERT INTO {table_id} (id_usuario_unico, id_usuario, contexto_usuario_json, data_ultima_atualizacao, telefone, id_empresa, fase_atual, id_matriz) VALUES ('{telefone+'-'+str(id_empresa)}', '', '''{context}''', '{Timestamp.now()}', '{telefone}', '{id_empresa}', '{fase}', '{self.id_matriz}')"
            logger.info(query)
            self._save_dataframe_to_bq(query=query)
            redis_client_set(f"save_empresa:{self.id_matriz}-{telefone}", id_empresa)
            # Save to cache
        except Exception as e:
            logger.error(f"Erro ao salvar empresa do usuário: {e}")
    
    @log_func_time
    def get_personality_context(self):
        try:
            cache_personality_context = redis_client_get(f"personality_context-{self.id_empresa}")
            if cache_personality_context:
                logger.info("Obtendo contexto da personalidade do cache...")
                return json.loads(cache_personality_context).get('personalidade')
            
            logger.info("Obtendo contexto da personalidade do BigQuery...")

            result = self._execute_query(f"SELECT personalidade FROM {GCP_BIGQUERY_DATASET}.contexto_personalidade")
            result_dataframe = result.to_dataframe()
            if result_dataframe.empty:
                personality_context = pdc.personality
            else:
                personality_context = result_dataframe.iloc[0].values[0]
            
            redis_client_set(
                f"personality_context-{self.id_empresa}",
                json.dumps({"personalidade": personality_context}),
                ex=8*60*60
            )
            return personality_context
        except Exception as e:
            logger.error(f"Erro ao obter contexto da personalidade: {e}")
            return pdc.personality

    @log_func_time
    def update_personality_context(self, context):
        try:
            logger.info("Atualizando contexto da personalidade...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_personalidade"
            personalidade = str(context.get('personalidade'))
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET personalidade = '''{personalidade}''', data_ultima_atualizacao = '{Timestamp.now()}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (personalidade, data_ultima_atualizacao, id_empresa) VALUES ('''{personalidade}''', '{Timestamp.now()}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)
            redis_client_set(
                f"personality_context-{self.id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da personalidade: {e}")

    @log_func_time
    def save_message(self, sent_by, message, telefone, model="", message_type="text", status="", prompt_tokens=0, completion_tokens=0, n_chars=0, n_seconds=0, 
        provider='z_api', message_id=None, id_contexto=None, situacao=None, departamento=None, colaborador=None, roles_to_save_redis=["user", "assistant"]):

        id_conversa = redis_client_get(f"current_conversation:{telefone}-{self.id_empresa}")
        
        if id_conversa: 
            id_conversa = id_conversa.decode('utf-8')
        else:  
            id_conversa = str(uuid.uuid4())
            redis_client_set(f"current_conversation:{telefone}-{self.id_empresa}", id_conversa)

        telefone = str(telefone)
        logger.info("Salvando mensagem...")
        try:
            if sent_by in roles_to_save_redis:
                # last messages cache
                last_messages = redis_client_get(f"last_messages-{telefone}-{self.id_empresa}")

                if last_messages:
                    last_messages = json.loads(last_messages)
                    last_messages += [{"enviado_por": sent_by, "mensagem": message}]
                else:
                    last_messages = [{"enviado_por": sent_by, "mensagem": message}]
            
                if self.id_empresa is None:
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_matriz}",
                        json.dumps(last_messages),
                        ex=8*60*60
                    )
                else:
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_empresa}",
                        json.dumps(last_messages),
                        ex=8*60*60
                    )

            table_id = f"{GCP_BIGQUERY_DATASET}.conversas"
            query = f"""
                INSERT INTO {table_id} 
                (id_conversa,enviado_por, id_usuario, data_envio, mensagem, status, telefone, id_empresa, prompt_tokens, completion_tokens, Tipo_mensagem, n_chars, n_seconds, model, id_mensagem, provedor_mensagem, id_contexto, situacao, departamento, colaborador) 
                VALUES (@id_conversa, @sent_by, '', @data_envio, @message, @status, @telefone, @id_empresa, @prompt_tokens, @completion_tokens, @message_type, @n_chars, @n_seconds, @model, @id_mensagem, @provedor_mensagem, @id_contexto, @situacao, @departamento, @colaborador)
            """
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("id_conversa", "STRING", id_conversa),
                    bigquery.ScalarQueryParameter("sent_by", "STRING", sent_by),
                    bigquery.ScalarQueryParameter("data_envio", "TIMESTAMP", Timestamp.now()),
                    bigquery.ScalarQueryParameter("message", "STRING", message),
                    bigquery.ScalarQueryParameter("status", "STRING", status),
                    bigquery.ScalarQueryParameter("telefone", "STRING", telefone),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                    bigquery.ScalarQueryParameter("prompt_tokens", "INT64", prompt_tokens),
                    bigquery.ScalarQueryParameter("completion_tokens", "INT64", completion_tokens),
                    bigquery.ScalarQueryParameter("message_type", "STRING", message_type),
                    bigquery.ScalarQueryParameter("n_chars", "INT64", n_chars),
                    bigquery.ScalarQueryParameter("n_seconds", "FLOAT64", n_seconds),
                    bigquery.ScalarQueryParameter("model", "STRING", model),
                    bigquery.ScalarQueryParameter("id_mensagem", "STRING", message_id),
                    bigquery.ScalarQueryParameter("provedor_mensagem", "STRING", provider),
                    bigquery.ScalarQueryParameter("id_contexto", "STRING", id_contexto),  
                    bigquery.ScalarQueryParameter("situacao", "STRING", situacao),
                    bigquery.ScalarQueryParameter("departamento", "STRING", departamento),
                    bigquery.ScalarQueryParameter("colaborador", "STRING", colaborador)
                ]
            )
            self._save_dataframe_to_bq(query=query, job_config=job_config)

        except Exception as e:
            logger.error(f"Erro ao salvar mensagem: {e}")

    @log_func_time
    def save_instance_data(self, instance_id, token):
        try:
            logger.info("Salvando dados da instância...")
            
            old_id_empresa, _ = get_from_instance(instance_id)
            old_instance_id, _ = get_from_empresa(self.id_empresa)
            connections.redis_client.delete(f"instances:{old_id_empresa}")
            connections.redis_client.delete(f"empresas:{old_instance_id}")
            redis_client_set(
                f"instances:{self.id_empresa}",
                json.dumps({"instance_id": instance_id, "token": token}),
                ex=8*60*60
            )
            redis_client_set(
                f"empresas:{instance_id}",
                json.dumps({"id_empresa": self.id_empresa, "token": token}),
                ex=8*60*60
            )
            
            table_id = f"{GCP_BIGQUERY_DATASET}.instances"
            exists_instance = self._check_if_row_exists(table_id, "instance_id", instance_id, use_id_empresa=False)
            exists_empresa = self._check_if_row_exists(table_id, "id_empresa", self.id_empresa)

            if exists_instance:
                query = f"UPDATE {table_id} SET instance_id = '', token = '' WHERE instance_id = '{instance_id}'"
                self._execute_query(query=query, use_id_empresa=False) # Limpa o registro conflitante

            if exists_empresa:
                use_id_empresa = True
                query = f"UPDATE {table_id} SET instance_id = '{instance_id}', token = '{token}', id_empresa = '{self.id_empresa}'"
            else:
                use_id_empresa = False
                query = f"INSERT INTO {table_id} (instance_id, token, id_empresa) VALUES ('{instance_id}', '{token}', '{self.id_empresa}')"

            self._execute_query(query=query, use_id_empresa=use_id_empresa)

        
        except Exception as e:
            logger.error(f"Erro ao salvar dados da instância: {e}")

    @log_func_time
    def save_responsible_data(self, email_responsavel_empresa, telefone_responsavel_empresa):
        try:
            logger.info("Salvando dados do responsável...")

            redis_client_set(
                f"responsavel:{self.id_empresa}",
                json.dumps({"email": email_responsavel_empresa, "telefone": telefone_responsavel_empresa}),
                ex=8*60*60
            )

            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_academia"

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET email_responsavel_empresa = '{email_responsavel_empresa}', telefone_responsavel_empresa = '{telefone_responsavel_empresa}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (id_empresa, email_responsavel_empresa, telefone_responsavel_empresa) VALUES ('{self.id_empresa}', '{email_responsavel_empresa}', '{telefone_responsavel_empresa}')"
                
            self._save_dataframe_to_bq(query=query)

            logger.info("Dados do responsável salvos com sucesso.")

        except Exception as e:
            logger.error(f"Erro ao salvar dados do responsável: {e}")

    @log_func_time
    def save_pacto_data(self, login, senha):
        try:
            logger.info("Salvando dados do Pacto...")

            redis_client_set(
                f"pacto:{self.id_empresa}",
                json.dumps({"login": login, "senha": senha}),
                ex=8*60*60
            )

            table_id = f"{GCP_BIGQUERY_DATASET}.pacto_users" 
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET login = '{login}', senha = '{senha}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (login, senha, id_empresa) VALUES ('{login}', '{senha}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)

        except Exception as e:
            logger.error(f"Erro ao salvar dados do Pacto: {e}")

    @log_func_time
    def get_pacto_data(self):
        try:
            cache_pacto = redis_client_get(f"pacto:{self.id_empresa}")
            if cache_pacto:
                logger.info("Obtendo dados do Pacto do cache...")
                user_name = json.loads(cache_pacto).get("login")
                user_password = json.loads(cache_pacto).get("senha")
                return user_name, user_password
            
            logger.info("Obtendo dados do Pacto do BigQuery...")
            result = self._execute_query(f"SELECT login, senha FROM {GCP_BIGQUERY_DATASET}.pacto_users")
            result_data = result.to_dataframe()

            # IMPORTANTE: Decidir o fazer nesses casos aqui onde não acha pacto_user
            if result_data.empty:
                return None, None
            user_name = result_data['login'].iloc[0]
            user_password = result_data['senha'].iloc[0]
            redis_client_set(
                f"pacto:{self.id_empresa}",
                json.dumps({"login": user_name, "senha": user_password}),
                ex=8*60*60
            )

            return user_name, user_password

        except Exception as e:
            logger.error(f"Erro ao obter dados do Pacto: {e}")
            return None, None
        
    @log_func_time
    def save_chain_context(self, data):
        try:
            logger.info("Salvando dados da cadeia...")
            table_id = f"{GCP_BIGQUERY_DATASET}.redes"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET redes_json = JSON '{json.dumps(data)}', data_ultima_atualizacao = '{Timestamp.now()}' WHERE id_empresa = '{self.id_empresa}'"
            else:
                query = f"INSERT INTO {table_id} (redes_json, data_ultima_atualizacao, id_empresa) VALUES (JSON '{json.dumps(data)}', '{Timestamp.now()}', '{self.id_empresa}')"
            self._save_dataframe_to_bq(query=query)
            redis_client_set(
                f"chain_context-{self.id_empresa}",
                json.dumps(data),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao salvar dados da cadeia: {e}")

    @log_func_time
    def get_chain_context(self, extra_query="") -> list:
        try:
            cache_chain_context = redis_client_get(f"chain_context-{self.id_empresa}")
            if cache_chain_context:
                logger.info("Obtendo contexto da cadeia do cache...")
                return json.loads(cache_chain_context)

            logger.info("Obtendo contexto da cadeia do BigQuery...")
            result = self._execute_query(f"SELECT redes_json FROM {GCP_BIGQUERY_DATASET}.redes", extra_query=extra_query)
            df = result.to_dataframe()

            if df.empty:
                logger.error("Query returned no results; cannot obtain chain context.")
                return None  # or return an empty list [] if that fits your use case

            chain_context = df.iloc[0]['redes_json']
            redis_client_set(
                f"chain_context-{self.id_empresa}",
                chain_context,
                ex=8*60*60
            )
            
            return json.loads(chain_context)
        except Exception as e:
            logger.error(f"Erro ao obter contexto da cadeia: {e}")
            return None

    @log_func_time
    def save_model_source(self, model_source):
        try:
            redis_client_set(
                f"model_source:{self.id_empresa}",
                model_source,
                ex=8*60*60
            )
            QUERY = f"UPDATE {GCP_BIGQUERY_DATASET}.contexto_academia SET model_source = '{model_source}' WHERE id_empresa = '{self.id_empresa}'"
            self._save_dataframe_to_bq(QUERY)
        except Exception as e:
            logger.error(f"Erro ao salvar modelo: {e}")

    @log_func_time
    def get_model_source(self):
        try:
            if check_if_key_exists(f"model_source:{self.id_empresa}"):
                logger.info("Obtendo fonte do modelo do cache...")
                cache_model_source = redis_client_get(f"model_source:{self.id_empresa}")
                return cache_model_source.decode('utf-8')
            logger.info("Obtendo fonte do modelo do BigQuery...")
            query = f"SELECT model_source FROM {GCP_BIGQUERY_DATASET}.contexto_academia WHERE id_empresa = '{self.id_empresa}'"
            result = self._execute_query(query)
            result_data = result.to_dataframe()
            model_source = result_data['model_source'].iloc[0]
            redis_client_set(
                f"model_source:{self.id_empresa}",
                str(model_source),
                ex=8*60*60
            )
            return model_source
        except Exception as e:
            logger.error(f"Erro ao obter fonte do modelo: {e}")
            return "openai"

    @log_func_time
    def get_last_messages(self, telefone, limit=6, roles_to_load_redis=["user", "assistant"]):
        telefone = str(telefone)
        try:
            if self.id_empresa is None:
                cache_last_messages = redis_client_get(f"last_messages-{telefone}-{self.id_matriz}")
            else:
                cache_last_messages = redis_client_get(f"last_messages-{telefone}-{self.id_empresa}")

            if cache_last_messages:
                json_last_messages = json.loads(cache_last_messages)
                if len(json_last_messages) >= limit:
                    json_last_messages = json_last_messages[-limit:]
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_empresa}",
                        json.dumps(json_last_messages),
                        ex=8*60*60
                    )
                return DataFrame(json_last_messages)
            logger.info("Obtendo últimas mensagens do BigQuery...")
            query = f"SELECT enviado_por, mensagem, model FROM {GCP_BIGQUERY_DATASET}.conversas WHERE telefone = '{telefone}'"
            extra_query = f" AND enviado_por IN {tuple(roles_to_load_redis)}"
            if telefone:
                extra_query += f" AND telefone = '{telefone}'"
            extra_query += f" ORDER BY data_envio DESC LIMIT {limit}"
            result = self._execute_query(query, extra_query=extra_query, use_id_empresa=False)
            result_df = result.to_dataframe()
            result_df = result_df.iloc[::-1]
            if self.id_empresa is None:
                redis_client_set(
                    f"last_messages-{telefone}-{self.id_matriz}",
                    json.dumps(result_df.to_dict(orient='records')),
                    ex=8*60*60
                )
            else:
                redis_client_set(
                    f"last_messages-{telefone}-{self.id_empresa}",
                    json.dumps(result_df.to_dict(orient='records')),
                    ex=8*60*60
                )
            return result_df
        except Exception as e:
            logger.error(f"Erro ao obter últimas mensagens: {e}")
            return None

    @log_func_time
    def get_messages(self, telefone: str, id_conversa: str):
        try:
            query = f"SELECT * FROM {GCP_BIGQUERY_DATASET}.conversas WHERE telefone = '{telefone}' AND id_conversa = '{id_conversa}'"
            extra_query = " order by data_envio asc"
            result = self._execute_query(query, extra_query=extra_query)
            result_df = result.to_dataframe()
            return result_df
        except Exception as e:
            logger.error(f"Erro ao obter mensagens: {e}")
            return None

    @log_func_time
    def save_connected_phone(self, telefone):
        try:
            logger.info(f"Verificando redis do connected_phone: {telefone}")
            telefone_conectado = redis_client_get(f"connected_phone:{self.id_empresa}")
            empresa_telefone = redis_client_get(f"empresas_telefone:{telefone}")
            if telefone_conectado: 
                if telefone_conectado.decode('utf-8') == telefone and empresa_telefone.decode('utf-8') == self.id_empresa:
                    logger.info("O que tá no redis é o mesmo")
                    return
            else:
                # Rotina de retirar telefones duplicados
                logger.info("Retirando telefones duplicados.")
                duplicated_query = f"UPDATE {GCP_BIGQUERY_DATASET}.contexto_academia SET telefone = NULL WHERE telefone = '{telefone}'"
                self._execute_query(duplicated_query, use_id_empresa=False)
                logger.info("Indo buscar no BQ.")
                redis_client_set(
                    f"connected_phone:{self.id_empresa}",
                    telefone,
                    ex=8*60*60
                )
                redis_client_set(
                    f"empresas_telefone:{telefone}",
                    self.id_empresa,
                    ex=8*60*60
                )
                table_id = f"{GCP_BIGQUERY_DATASET}.contexto_academia"
                if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                    QUERY = f"UPDATE {table_id} SET telefone = '{telefone}' WHERE id_empresa = '{self.id_empresa}'"
                else:
                    QUERY = f"INSERT INTO {table_id} (telefone, id_empresa) VALUES ('{telefone}', '{self.id_empresa}')"

                self._save_dataframe_to_bq(QUERY)
        except Exception as e:
            logger.error(f"Erro ao salvar telefone conectado: {e}")
    
    @log_func_time
    def get_connected_phone(self):
        try:
            telefone_conectado = redis_client_get(f"connected_phone:{self.id_empresa}")
            if telefone_conectado:
                return telefone_conectado.decode('utf-8')
            query = f"SELECT telefone FROM {GCP_BIGQUERY_DATASET}.contexto_academia"
            result = self._execute_query(query)
            result_data = result.to_dataframe()
            if result_data.empty:
                logger.info("Nenhum telefone conectado encontrado.")
                return None
            telefone = result_data['telefone'].iloc[0]
            redis_client_set(
                f"connected_phone:{self.id_empresa}",
                telefone,
                ex=8*60*60
            )
            return telefone
        except Exception as e:
            logger.error(f"Erro ao obter telefone conectado: {e}")
            return None

    @log_func_time
    def set_chain_status(self, status: bool = True):
        try:
            QUERY = f"UPDATE {GCP_BIGQUERY_DATASET}.instances SET is_rede = {status} WHERE id_empresa = '{self.id_empresa}'"
            self._save_dataframe_to_bq(QUERY)
        except Exception as e:
            logger.error(f"Erro ao salvar status da cadeia: {e}")

    @log_func_time
    def save_message_status(self, data):
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.status_envios"

            id_mensagem = data.get("id", None)
            status_mensagem = data.get("status", None)
            provedor_mensagem = data.get("provedor_mensagem", "z_api")
            id_empresa = self.id_empresa
            is_group = data.get("isGroup", False)
            if not is_group:
                telefone = parse_phone(data.get("phone", None))
            else:
                telefone = data.get("phone", None)
            data_registro = Timestamp.now()

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("id_mensagem", "STRING", id_mensagem),
                    bigquery.ScalarQueryParameter("status_mensagem", "STRING", status_mensagem),
                    bigquery.ScalarQueryParameter("provedor_mensagem", "STRING", provedor_mensagem),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", id_empresa),
                    bigquery.ScalarQueryParameter("telefone", "STRING", telefone),
                    bigquery.ScalarQueryParameter("data_registro", "TIMESTAMP", data_registro),
                ]
            )

            query = f"""
                INSERT INTO {table_id} 
                (id_mensagem, status_mensagem, provedor_mensagem, id_empresa, telefone, data_registro)
                VALUES (@id_mensagem, @status_mensagem, @provedor_mensagem, @id_empresa, @telefone, @data_registro)
            """

            result = self._save_dataframe_to_bq(query, job_config)
            
            logger.info("Status da mensagem salvo com sucesso.")
            logger.info(f"Status da mensagem: {result}")
        except Exception as e:
            logger.error(f"Erro ao salvar status da mensagem: {e}")

    @log_func_time
    def save_conversation_analysis(
            self,
            phone,
            prompt,
            analysis,
            success
    ):
        """Salva a análise de uma conversa."""
        logger.info("Salvando análise da conversa...")
        id_conversa = redis_client_get(f"current_conversation:{phone}-{self.id_empresa}")
        if id_conversa:
            id_conversa = id_conversa.decode('utf-8')
        else:
            id_conversa = ''
        if success:
            connections.redis_client.delete(f"current_conversation:{phone}-{self.id_empresa}")
        try:
            query = f"""
                INSERT INTO {GCP_BIGQUERY_DATASET}.conversas_analise (phone, analysis, success, id_empresa, id_conversa, data_analise, prompt)
                VALUES (@phone, @analysis, @success, @id_empresa, @id_conversa, @data_analise, @prompt)
            """
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("phone", "STRING", phone),
                    bigquery.ScalarQueryParameter("analysis", "STRING", analysis),
                    bigquery.ScalarQueryParameter("success", "BOOL", success),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                    bigquery.ScalarQueryParameter("id_conversa", "STRING", id_conversa),
                    bigquery.ScalarQueryParameter("data_analise", "TIMESTAMP", Timestamp.now()),
                    bigquery.ScalarQueryParameter("prompt", "JSON", prompt)
                ]
            )
            # self._save_dataframe_to_bq(query, job_config=job_config)
            client = self._get_client()
            _ = client.query(query, job_config=job_config)
            logger.info("Análise da conversa salva com sucesso.")
        except Exception as e:
            logger.error("Erro ao salvar análise da conversa: %s", e)

    @log_func_time
    def save_notification_schema(
        self, descricao_original, schema_notificacao,
        notification_type, category=None
    ):
        """Salva o schema de notificações de uma empresa."""
        try:
            logger.info("Salvando schema de notificações...")
            table_id = f"{GCP_BIGQUERY_DATASET}.notificacoes_schema"
            if category:
                extra_query = f" AND category = '{category}'"
            else:
                extra_query = ""
            if self._check_if_row_exists(
                table_id, "notification_type", notification_type,
                use_id_empresa=True, extra_query=extra_query
            ):
                query = f"""
                    UPDATE {table_id}
                    SET schema_notificacao = @schema_notificacao,
                        descricao_original = @descricao_original,
                        data_envio = @data_envio
                """

                if category:
                    query += ", category = @category "

                query += "WHERE id_empresa = @id_empresa AND notification_type = @notification_type"

            else:
                query = (
                    f"""
                        INSERT INTO {table_id} (id_empresa, schema_notificacao, descricao_original, data_envio, notification_type
                    """
                    + (", category" if category else "") + ") "
                )
                query += (
                    "VALUES (@id_empresa, @schema_notificacao, @descricao_original, @data_envio, @notification_type"
                    + (", @category" if category else "") + ")"
                )
            query_parameters = [
                bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                bigquery.ScalarQueryParameter("schema_notificacao", "JSON", schema_notificacao),
                bigquery.ScalarQueryParameter("descricao_original", "STRING", descricao_original),
                bigquery.ScalarQueryParameter("notification_type", "STRING", notification_type),
                bigquery.ScalarQueryParameter("data_envio", "TIMESTAMP", Timestamp.now()),
            ]
            if category:
                query_parameters.append(
                    bigquery.ScalarQueryParameter("category", "STRING", category)
                )
            job_config = bigquery.QueryJobConfig(
                query_parameters=query_parameters
            )
            self._save_dataframe_to_bq(query, job_config=job_config)
            cache_key = f"notification_scheme:{notification_type}:{self.id_empresa}"
            if category:
                cache_key += f":{category}"
            redis_client_set(
                cache_key,
                json.dumps(schema_notificacao),
                ex=8*60*60
            )
            logger.info(f"Esquema de notificação processado e armazenado com sucesso")
            logger.info("Schema de notificações salvo com sucesso.")

        except Exception as e:
            logger.error(f"Erro ao salvar schema de notificações: {e}")

    @log_func_time
    def get_notification_schema(self, notification_type, category=None):
        """Obtém o schema de notificações de uma empresa."""
        try:
            cache_key = f"notification_scheme:{notification_type}:{self.id_empresa}"
            if category:
                cache_key += f":{category}"
            else:
                keys = connections.redis_client.keys(f"{cache_key}:*")
                if keys:
                    return [{
                        "category": key.decode().split(":")[-1],
                        **json.loads(connections.redis_client.get(key.decode()))
                    } for key in keys]

            cache_notification_schema = redis_client_get(cache_key)
            if cache_notification_schema:
                return json.loads(cache_notification_schema)

            logger.info("Obtendo schema de notificações do BigQuery...")
            table_id = f"{GCP_BIGQUERY_DATASET}.notificacoes_schema"
            query = f"SELECT schema_notificacao, category FROM {table_id} WHERE notification_type = '{notification_type}'"
            if category:
                query += f" AND category = '{category}'"

            result = self._execute_query(query).to_dataframe()

            if result.empty:
                logger.warning(f"Nenhum schema de notificações {notification_type} encontrado para a empresa: {self.id_empresa}")
                return None

            if not category:
                result = result[['category', 'schema_notificacao']].to_dict(orient='records')
                for item in result:
                    if item.get('category') is None:
                        current_key = cache_key
                    else:
                        current_key = f"{cache_key}:{item.get('category')}"
                    redis_client_set(
                        current_key,
                        item.get('schema_notificacao'),
                        ex=8*60*60
                    )
                schema_notificacao = [{
                        "category": item.get('category'),
                        **json.loads(item.get('schema_notificacao'))
                    } for item in result]
                if len(schema_notificacao) == 1:
                    schema_notificacao = schema_notificacao[0]
            else:
                result = result['schema_notificacao'].iloc[0]
                schema_notificacao = json.loads(result)
                redis_client_set(
                    cache_key,
                    json.dumps(schema_notificacao),
                    ex=8*60*60
                )

            return schema_notificacao

        except Exception as e:
            logger.error(f"Erro ao obter schema de notificações: {e}")
            return None

    @log_func_time
    def save_meta_diaria(
            self,
            id_conversa: str,
            phone: str,
            classification: str,
            action_description: str
    ):
        """Salva o resultado da meta diária."""
        # TODO: Criar a tabela de metas diárias
        id_conversa = redis_client_get(f"current_conversation:{phone}-{self.id_empresa}")
        if id_conversa:
            id_conversa = id_conversa.decode('utf-8')
        else:
            id_conversa = ''
        try:
            query = f"""
                INSERT INTO {GCP_BIGQUERY_DATASET}.metas_diarias (phone, classification, action_description, id_empresa, id_conversa, data_analise)
                VALUES (@phone, @classification, @action_description, @id_empresa, @id_conversa, @data_analise)
            """
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("phone", "STRING", phone),
                    bigquery.ScalarQueryParameter("classification", "STRING", classification),
                    bigquery.ScalarQueryParameter("action_description", "STRING", action_description),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                    bigquery.ScalarQueryParameter("id_conversa", "STRING", id_conversa),
                    bigquery.ScalarQueryParameter("data_analise", "TIMESTAMP", Timestamp.now())
                ]
            )
            self._save_dataframe_to_bq(query, job_config=job_config)
            connections.redis_client.delete(f"meta_diaria:{id_conversa}")
            connections.redis_client.set(f"queue_free:{self.id_empresa}:{phone}", "True")
        except Exception as e:
            logger.error("Erro ao salvar meta diária: %s", e)

    @log_func_time
    def update_messager_channel(self, channel):
        try:
            logger.info("Atualizando canal de mensagens...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_academia"
            data_atualizacao = Timestamp.now()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET messager_channel = @messager_channel, data_ultima_atualizacao = @data_ultima_atualizacao WHERE id_empresa = @id_empresa"
            else:
                query = f"INSERT INTO {table_id} (messager_channel, data_ultima_atualizacao, id_empresa)) VALUES (@messager_channel, @data_ultima_atualizacao, @id_empresa)"
            
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("messager_channel", "STRING", channel),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                ]
            )

            self._save_dataframe_to_bq(query, job_config=job_config)
            redis_client_set(
                f"messager_channel:{self.id_empresa}",
                channel,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar canal de mensagens: {e}")
    
    @log_func_time
    def save_gymbot_token(self, token):
        try:
            logger.info("Atualizando token do GymBot...")
            table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_tokens"
            data_atualizacao = Timestamp.now()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET token = @token, data_ultima_atualizacao = @data_ultima_atualizacao WHERE id_empresa = @id_empresa"
            else:
                query = f"INSERT INTO {table_id} (id_empresa, token, data_ultima_atualizacao) VALUES (@id_empresa, @token, @data_ultima_atualizacao)"
            
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                    bigquery.ScalarQueryParameter("token", "STRING", token),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                ]
            )

            self._save_dataframe_to_bq(query, job_config=job_config)
            redis_client_set(
                f"gymbot_token:{self.id_empresa}",
                token,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar token do GymBot no BQ: {e}")
    
    @log_func_time
    def get_gymbot_token(self):
        try:
            if check_if_key_exists(f"gymbot_token:{self.id_empresa}"):
                logger.info("Obtendo token do GymBot do cache...")
                cache_token = redis_client_get(f"gymbot_token:{self.id_empresa}")
                return cache_token.decode('utf-8')
            logger.info("Obtendo token do GymBot do BQ...")
            table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_tokens"

            query = f"SELECT token FROM {table_id}"
            result = self._execute_query(query).to_dataframe()
            if result.empty:
                logger.warning("Nenhum token gymbot encontrado.")
                return None
            token = result['token'].iloc[0]
            return token
        except Exception as e:
            logger.error(f"Erro ao obter token do GymBot: {e}")
            return None
   
    @log_func_time
    def save_departament_descriptions(self, departments):
        logger.info("Salvando descrições dos departamentos...")
        data_atualizacao = Timestamp.now()
        table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_departamentos"

        cache_departments = []
        self.get_departament_descriptions()
        if check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}"):
            cache_departments = redis_client_get(f"gymbot:departament_descriptions:{self.id_empresa}").decode('utf-8')
            cache_departments = json.loads(cache_departments)

        for departament in departments:
            logger.info(f"Salvando descrição do departamento: {departament.get('name')}")
            try:
                exists_departament = self._check_if_row_exists(table_id, "id_departamento", departament.get("id"), use_id_empresa=True)
                cache_departments = [d for d in cache_departments if d.get('id_departamento') != departament.get("id")]
                if exists_departament:
                    query = f"UPDATE {table_id} SET departamento = @departamento, descricao = @descricao, data_ultima_atualizacao = @data_ultima_atualizacao WHERE id_departamento = @id_departamento AND id_empresa = @id_empresa"
                else:
                    query = f"INSERT INTO {table_id} (departamento, descricao, id_departamento, id_empresa, data_ultima_atualizacao) VALUES (@departamento, @descricao, @id_departamento, @id_empresa, @data_ultima_atualizacao)"
                
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter("departamento", "STRING", departament.get("name")),
                        bigquery.ScalarQueryParameter("descricao", "STRING", departament.get("descricao")),
                        bigquery.ScalarQueryParameter("id_departamento", "STRING", departament.get("id")),
                        bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                        bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    ]
                )
                self._save_dataframe_to_bq(query, job_config=job_config)
                cache_departments.append({
                    "id_departamento": departament.get("id"),
                    "departamento": departament.get("name"),
                    "descricao": departament.get("descricao")
                })
            except Exception as e:
                logger.error(f"Erro ao salvar descrição do departamento: {e}") 

        logger.info("Salvando descrições dos departamentos no cache...")
        redis_client_set(
            f"gymbot:departament_descriptions:{self.id_empresa}",
            json.dumps(cache_departments),
            ex=8*60*60
        )
    

    def delete_departament_descriptions(self, departments):
        """Deleta as descrições dos departamentos da empresa."""
        try:
            logger.info("Deletando descrições dos departamentos...")
            table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_departamentos"
            cache_departments = []
            has_cache = check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}")
            if has_cache:
                cache_departments = redis_client_get(f"gymbot:departament_descriptions:{self.id_empresa}").decode('utf-8')
                cache_departments = json.loads(cache_departments)

            for id_departamento in departments:
                query = f"DELETE FROM {table_id} WHERE id_departamento = '{id_departamento}'"
                self._execute_query(query)

                cache_departments = [dep for dep in cache_departments if dep.get('id_departamento') != id_departamento]

            if has_cache:
                redis_client_set(
                    f"gymbot:departament_descriptions:{self.id_empresa}",
                    json.dumps(cache_departments),
                    ex=8*60*60
                )

            logger.info("Descrições dos departamentos deletadas com sucesso.")
        except Exception as e:
            logger.error(f"Erro ao deletar descrições dos departamentos: {e}")
    
    @log_func_time
    def get_departament_descriptions(self):
        try:
            if check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}"):
                logger.info("Obtendo descrições dos departamentos do cache...")
                cache_departments = redis_client_get(f"gymbot:departament_descriptions:{self.id_empresa}")
                return json.loads(cache_departments)
            logger.info("Obtendo descrições dos departamentos do BQ...")
            table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_departamentos"
            query = f"SELECT id_departamento, departamento, descricao FROM {table_id}"
            result = self._execute_query(query).to_dataframe()
            result['departamento'] = result['departamento'].str.lower()
            result = result[result['departamento'] != 'conversasai']
            if result.empty:
                logger.warning(f"Nenhuma descrição encontrada para a empresa: {self.id_empresa}")
                return []
            departament_descriptions = result[['id_departamento', 'departamento', 'descricao']].to_dict(orient='records')
            redis_client_set(
                f"gymbot:departament_descriptions:{self.id_empresa}",
                json.dumps(departament_descriptions),
                ex=8*60*60
            )
            return departament_descriptions


        except Exception as e:
            logger.error(f"Erro ao obter descrições dos departamentos: {e}")
            return []

    @log_func_time
    def save_auth_api_key(self, api_key, user, active):
        """
        Salva a chave de API no BigQuery e no Redis.
        *Sobre a compatibilidade com a chave unificada de antes:*
        - Se não for passado id_empresa:
            - A chave unificada é a única que existe.
            - Ela é a única que será usada para verificar se a chave já existe.
        - Se for passado id_empresa:
            - Será verificado se a chave já existe para a empresa.
            - Se existir, será atualizado o campo last_data.
            - Se não existir, será criada uma nova chave.

        Params:
            api_key (str): Chave de API a ser salva.
            user (str): Usuário associado à chave de API.
            active (bool): Indica se a chave está ativa ou não.
        """
        try:
            logger.info("Salvando chave de API no BigQuery...")

            use_id_empresa = self.id_empresa is not None

            table_id = f"{GCP_BIGQUERY_DATASET}.api_keys"
            exists_api_key = self._check_if_row_exists(table_id, "api_key", api_key, use_id_empresa=use_id_empresa)
            exists_user = self._check_if_row_exists(table_id, "user", user, use_id_empresa=use_id_empresa)

            if exists_api_key and exists_user:
                logger.info("Chave de API ja existe.")
                query = f"""
                    UPDATE {table_id}
                    SET last_data = CURRENT_TIMESTAMP()
                    WHERE api_key = '{api_key}' AND user = '{user}' AND active = {active}
                """
                if self.id_empresa:
                    query += f" AND id_empresa = '{self.id_empresa}'"
            else:
                query = f"""
                    INSERT INTO {table_id} (api_key, user, creation_data, last_data, active, id_empresa)
                    VALUES ('{api_key}', '{user}', CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), {active}, '{self.id_empresa}')
                """

            self._save_dataframe_to_bq(query=query)
            logger.info("Chave de API salva com sucesso.")
            if active:
                redis_client_set(
                    f"api_keys:{self.id_empresa}:{api_key}",
                    json.dumps({"api_key": api_key, "user": user}),
                    ex=8*60*60
                )
            else:
                connections.redis_client.delete(f"api_keys:{self.id_empresa}:{api_key}")

        except Exception as e:
            logger.error(f"Erro ao salvar chave de API: {e}")

    @log_func_time
    def get_api_key_integration(self, api_key):
        """Busca a chave de API e user."""
        try:
            if not self.id_empresa:
                cache_api_key = redis_client_get(f"api_keys:{api_key}")
                if cache_api_key:
                    logger.info("Obtendo chave de API do cache...")
                    json_api_key: dict = json.loads(cache_api_key)
                    return json_api_key.get("api_key"), json_api_key.get("user")

                query = f"""
                SELECT api_key, user
                FROM {GCP_BIGQUERY_DATASET}.api_keys
                WHERE api_key = @api_key
                """
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter("api_key", "STRING", api_key)
                    ]
                )
            else:
                cache_api_key = redis_client_get(f"api_keys:{self.id_empresa}:{api_key}")
                if cache_api_key:
                    logger.info("Obtendo chave de API do cache...")
                    json_api_key: dict = json.loads(cache_api_key)
                    return json_api_key.get("api_key"), json_api_key.get("user")

                query = f"""
                SELECT api_key, user 
                FROM {GCP_BIGQUERY_DATASET}.api_keys 
                WHERE api_key = @api_key
                AND id_empresa = @id_empresa
                """

                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter("api_key", "STRING", api_key),
                        bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa)
                    ]
                )

            client = self._get_client()

            query_job = client.query(query, job_config=job_config)
            results = query_job.result()

            rows = list(results)

            if not rows:
                logger.warning(f"Nenhuma API Key encontrada para: {api_key}")
                return False, False
            row = rows[0]
            logger.info(f"API Key encontrada: {row.api_key}, User: {row.user}")

            redis_client_set(
                f"api_keys:{api_key}",
                json.dumps({"api_key": row.api_key, "user": row.user}),
                ex=8*60*60
            )

            return row.api_key, row.user

        except Exception as e:
            logger.error(f"Erro ao buscar a chave de API no BigQuery: {e}")
            return False, False

    @log_func_time
    def save_auth_session(self, api_key, token, expiration_time):
        try:
            logger.info("Salvando sessão de autenticação no BigQuery...")
            table_id = f"{GCP_BIGQUERY_DATASET}.sessions"

            query = f"""
                INSERT INTO `{table_id}` (api_key, token, creation, expiration, id_empresa)
                VALUES (@api_key, @token, CURRENT_TIMESTAMP(), @expiration, @id_empresa)
            """
            creation_date= Timestamp.now()

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("api_key", "STRING", api_key),
                    bigquery.ScalarQueryParameter("token", "STRING", token),
                    bigquery.ScalarQueryParameter("creation", "TIMESTAMP", creation_date),
                    bigquery.ScalarQueryParameter("expiration", "INTEGER", expiration_time),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa)
                ]
            )

            result = self._save_dataframe_to_bq(query, job_config)
            
            logger.info("Status da mensagem salvo com sucesso.")
            logger.info(f"Status da mensagem: {result}")
        except Exception as e:
            logger.error(f"Erro ao salvar status da mensagem: {e}")

    @log_func_time
    def get_messager_channel(self):
        try:
            logger.info("buscando canal de mensagens...")
            if check_if_key_exists(f"messager_channel:{self.id_empresa}"):
                logger.info("Obtendo canal de mensagens do cache...")
                cache_channel = redis_client_get(f"messager_channel:{self.id_empresa}")
                return cache_channel.decode('utf-8')

            logger.info("Obtendo canal de mensagens do BigQuery...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_academia"
            query = f"SELECT messager_channel FROM {table_id}"
            result = self._execute_query(query).to_dataframe()
            if result.empty:
                logger.warning(f"Nenhum canal de mensagens encontrado para a empresa: {self.id_empresa}, escolhendo z_api como padrão")
                return "z_api"
            channel = result['messager_channel'].iloc[0]
            if not channel:
                return "z_api"
            redis_client_set(
                f"messager_channel:{self.id_empresa}",
                channel,
                ex=8*60*60
            )
            return channel
        except Exception as e:
            logger.error(f"Erro ao atualizar canal de mensagens: {e}")
            return "z_api"
        
    @log_func_time
    def delete_api_key(self, api_key):
        try:
            logger.info("Deletando chave de API no BigQuery...")
            table_id = f"{GCP_BIGQUERY_DATASET}.api_keys"

            if not self.get_api_key_integration(api_key):
                logger.warning("Chave de API não encontrada para exclusão.")
                return {"message": "API key not found"}

            query = f"""
                DELETE FROM `{table_id}`
                WHERE api_key = '{api_key}'
            """
            if self.id_empresa:
                query += f" AND id_empresa = '{self.id_empresa}'"

            client=self._get_client()

            client.query(query).result()
            logger.info("Chave de API deletada com sucesso.")
            if self.id_empresa:
                connections.redis_client.delete(f"api_keys:{self.id_empresa}:{api_key}")
            else:
                connections.redis_client.delete(f"api_keys:{api_key}")

        except Exception as e:
            logger.error(f"Erro ao deletar chave de API: {e}")

    @log_func_time
    def get_api_keys(self, name=None, page=1, limit=10):
        try:
            logger.info("Consultando chaves de API no BigQuery...")
            offset = (page - 1) * limit

            query = f"""
                SELECT api_key, user
                FROM `{GCP_BIGQUERY_PROJECT_ID}.{GCP_BIGQUERY_DATASET}.api_keys`
            """

            if name:
                query += f" WHERE LOWER(user) LIKE LOWER('%{name}%')"

            if self.id_empresa:
                query += f" AND id_empresa = '{self.id_empresa}'"

            query += f" LIMIT {limit} OFFSET {offset}"

            client = self._get_client()
            results = client.query(query).result()

            api_keys = [{"api_key": row.api_key, "user": row.user} for row in results]
            logger.info(f"Encontradas {len(api_keys)} chaves de API.")
            return api_keys

        except Exception as e:
            logger.error(f"Erro ao consultar chaves de API: {e}")
            return []

    @log_func_time
    def save_memories(self, data: dict, phone: str):
        logger.info("Salvando memórias...")
        if self._check_if_row_exists(f"{GCP_BIGQUERY_DATASET}.memories", "phone", phone):
            query = f"UPDATE {GCP_BIGQUERY_DATASET}.memories SET " + \
            "memory = @memory, data_ultima_atualizacao = @data_ultima_atualizacao " + \
            "WHERE phone = @phone AND id_empresa = @id_empresa"
        else:
            query = f"INSERT INTO {GCP_BIGQUERY_DATASET}.memories " + \
            "(phone, memory, id_empresa, data_ultima_atualizacao) VALUES " + \
            "(@phone, @memory, @id_empresa, @data_ultima_atualizacao)"

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("phone", "STRING", phone),
                bigquery.ScalarQueryParameter("memory", "STRING", json.dumps(data)),
                bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", Timestamp.now())
            ]
        )

        logger.info(query)

        self._save_dataframe_to_bq(query, job_config=job_config)

        connections.redis_client.set(
            f"memories:{self.id_empresa}:{phone}",
            json.dumps(data),
            ex=8*60*60
        )

    @log_func_time
    def get_dados_bi(
        self,
        data_inicio: str,
        data_fim: str,
        type: str = Literal[
            "total_atendimentos",
            "frequencia_atendimento",
            "tempo_medio_atendimento",
        ],
    ):
        """Obtém dados de BI do BigQuery."""
        logger.info("Obtendo dados de BI...")
        with open(f"src/data/sql/bi/{type}.sql", "r") as file:
            query = file.read()

        if query:
            query = query.format(
                dataset=GCP_BIGQUERY_DATASET,
                id_empresa=self.id_empresa,
                data_inicio=data_inicio,
                data_fim=data_fim,
            )

        result = self._execute_query(
            query, use_id_empresa=False).to_dataframe()

        if result.empty:
            logger.warning(f"Nenhum dado encontrado para o tipo: {type}")
            return []

        return result

    @log_func_time
    def get_memories(self, phone: str) -> dict:
        logger.info("Obtendo memórias...")
        if check_if_key_exists(f"memories:{self.id_empresa}:{phone}"):
            logger.info("Obtendo memórias do cache...")
            cache_memories = redis_client_get(f"memories:{self.id_empresa}:{phone}")
            return json.loads(cache_memories)

        logger.info("Obtendo memórias do BigQuery...")
        query = f"SELECT memory FROM {GCP_BIGQUERY_DATASET}.memories WHERE phone = '{phone}'"
        result = self._execute_query(query).to_dataframe()
        if result.empty:
            logger.info("Nenhuma memória encontrada.")
            return {}

        memories = result['memory'].iloc[0]

        redis_client_set(
            f"memories:{self.id_empresa}:{phone}",
            memories,
            ex=8*60*60
        )

        return json.loads(memories)

    def register_log(self, data, table):
        logger.info("Registrando log de requisição...")
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.logs_{table}"

            rows_to_insert = [data]

            client = self._get_client()
            
            errors = client.insert_rows_json(table_id, rows_to_insert)

            if errors:
                logger.error(f"Erros ao registrar log de requisição: {errors}")
            else:
                logger.info("Log de requisição registrado com sucesso")

        except Exception as e:
            logger.error(f"Erro ao registrar log de requisição: {e}")

    def register_indicator(self, data, indicador):
        """Registra um indicador no BigQuery."""
        logger.info("Registrando indicador...")
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.indicadores"
            id_conversa = data.get("id_conversa", "")
            id_empresa = data.get("id_empresa", self.id_empresa)
            telefone = data.get("telefone", "")
            identificador = data.get("identificador", "")
            data_hora_evento = data.get("data_hora_evento", Timestamp.now())
            meta = data.get("meta", {})
            if meta == {}:
                meta = None

            query = f"""
                INSERT INTO `{table_id}` 
                (indicador, identificador, id_empresa, telefone, id_conversa, data_hora_evento, meta)
                VALUES (@indicador, @identificador, @id_empresa, @telefone, @id_conversa, @data_hora_evento, @meta)
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("indicador", "STRING", indicador),
                    bigquery.ScalarQueryParameter("identificador", "STRING", identificador),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", str(id_empresa)),
                    bigquery.ScalarQueryParameter("telefone", "STRING", telefone),
                    bigquery.ScalarQueryParameter("id_conversa", "STRING", id_conversa),
                    bigquery.ScalarQueryParameter("data_hora_evento", "TIMESTAMP", data_hora_evento),
                    bigquery.ScalarQueryParameter("meta", "JSON", meta),
                ]
            )

            client = self._get_client()
            client.query(query, job_config=job_config).result()
            logger.info("Indicador registrado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao registrar indicador: {e}")

    @log_func_time
    def get_campaigns_context(self, id_campanha):
        try:
            if check_if_key_exists(f"contexto_campanhas:{self.id_empresa}"):
                logger.info("Obtendo campanhas do cache...")
                cache_campaigns = redis_client_get(f"contexto_campanhas:{self.id_empresa}")
                if id_campanha:
                    return json.loads([x for x in cache_campaigns if x['id_campanha'] == id_campanha])
                return json.loads(cache_campaigns)
            logger.info("Obtendo campanhas do BigQuery...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_campanhas"
            query = f"""
                SELECT 
                    id_empresa, 
                    id_campanha, 
                    nome, 
                    instrucao, 
                    keyword,
                    FORMAT_TIMESTAMP('%d/%m/%Y %H:%M:%S', data_inicio) as data_inicio, 
                    FORMAT_TIMESTAMP('%d/%m/%Y %H:%M:%S', data_fim) as data_fim,
                    imagem, 
                    is_template, 
                    FORMAT_TIMESTAMP('%d/%m/%Y %H:%M:%S', data_atualizacao) as data_atualizacao,
                    whatsapp_link
                FROM {table_id}
            """
            if id_campanha:
                query+=f" WHERE id_campanha = '{id_campanha}'"
            result = self._execute_query(query).to_dataframe()
            if result.empty:
                logger.warning(f"Nenhuma campanha com id {id_campanha} encontrada para a empresa: {self.id_empresa}")
                return []
            result_dict = result.to_dict(orient='records')
            if not id_campanha:
                redis_client_set(
                    f"contexto_campanhas:{self.id_empresa}",
                    json.dumps(result_dict),
                    ex=8*60*60
                )
            return result_dict
        except Exception as e:
            logger.error(f"Erro ao obter campanhas: {e}")
            return None
    
    @log_func_time
    def update_campanha_context(self, id_campanha, data):
        try:
            logger.info("Atualizando contexto da campanha...")
            table_id = f"{GCP_BIGQUERY_DATASET}.contexto_campanhas"
            data_atualizacao = Timestamp.now()

            st_base64 = data.get("imagem")
            public_img_url = None
            if st_base64:
                bucket = Bucket(BUCKET_NAME_CAMPANHA)
                format_type, img_base64_str = st_base64.split('base64,')
                format_image = re.findall(r"(?<=data:)(.*)(?=;)", format_type)[0]
                base64_image = base64.b64decode(img_base64_str)
                public_img_url = bucket.upload(base64_image, f"{self.id_empresa}/{id_campanha}", True, True, format_image)

            data_inicio = get_date(data.get("data_inicio"), format="%d/%m/%Y %H:%M:%S")
            data_fim = get_date(data.get("data_fim"), format="%d/%m/%Y %H:%M:%S") or None

            connected_telefone = self.get_connected_phone()
            keyword = data.get("keyword", None)
            whatsapp_link = f"https://api.whatsapp.com/send?phone={connected_telefone}&text={urllib.parse.quote(keyword, safe='')}"

            campanhas = self.get_campaigns_context(None)
            campanha = [x for x in campanhas if x['id_campanha'] == id_campanha]
            if campanha:
                query = (
                    f"""
                    UPDATE {table_id} SET
                      nome = @nome, instrucao = @instrucao, keyword = @keyword, data_inicio = @data_inicio, data_fim = @data_fim, 
                      imagem = @imagem, is_template = @is_template, data_atualizacao = @data_ultima_atualizacao, whatsapp_link = @whatsapp_link
                    WHERE
                      id_empresa = @id_empresa AND id_campanha = @id_campanha"""
                )
            else:
                query =(
                    f"""
                    INSERT INTO {table_id} 
                      (id_empresa, id_campanha, nome, instrucao, keyword, data_inicio, data_fim, imagem, is_template, data_atualizacao, whatsapp_link)
                    VALUES
                      (@id_empresa, @id_campanha, @nome, @instrucao, @keyword, @data_inicio, @data_fim, @imagem, @is_template, @data_ultima_atualizacao, @whatsapp_link)"""
                )
            
            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", self.id_empresa),
                    bigquery.ScalarQueryParameter("id_campanha", "STRING", id_campanha),
                    bigquery.ScalarQueryParameter("nome", "STRING", data.get("nome")),
                    bigquery.ScalarQueryParameter("instrucao", "STRING", data.get("instrucao")),
                    bigquery.ScalarQueryParameter("keyword", "STRING", keyword),
                    bigquery.ScalarQueryParameter("data_inicio", "TIMESTAMP", data_inicio),
                    bigquery.ScalarQueryParameter("data_fim", "TIMESTAMP", data_fim),
                    bigquery.ScalarQueryParameter("imagem", "STRING", public_img_url),
                    bigquery.ScalarQueryParameter("is_template", "BOOL", data.get("is_template")),
                    bigquery.ScalarQueryParameter("data_ultima_atualizacao", "TIMESTAMP", data_atualizacao),
                    bigquery.ScalarQueryParameter("whatsapp_link", "STRING", whatsapp_link),
                ]
            )

            self._save_dataframe_to_bq(query, job_config=job_config)

            data.update({
                "id_empresa": self.id_empresa,
                "id_campanha": id_campanha,
                "imagem": public_img_url,
                "data_atualizacao": datetime.strftime(data_atualizacao, "%d/%m/%Y %H:%M:%S"),
                "whatsapp_link": whatsapp_link
            })

            if check_if_key_exists(f"contexto_campanhas:{self.id_empresa}"):
                if "UPDATE" in query:
                    logger.info("Atualizando campanhas no cache...")
                    outras_campanhas = [x for x in campanhas if x['id_campanha'] != id_campanha]
                    campanhas = outras_campanhas + [data]
                    redis_client_set(
                        f"contexto_campanhas:{self.id_empresa}",
                        json.dumps(campanhas),
                        ex=8*60*60
                    )
                    return
                
                logger.info("Inserindo campanhas no cache...")
                campanhas.append(data)
                redis_client_set(
                    f"contexto_campanhas:{self.id_empresa}",
                    json.dumps(campanhas),
                    ex=8*60*60
                )
                return
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da campanha: {e}")

    @log_func_time
    def create_config(self, data: dict):
        """Cria um config no BigQuery e no Redis."""
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.configuration"
            if not self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                redis_client_set(f"config:{self.id_empresa}", json.dumps(data))
                logger.info("Criando config...")
                logger.info("Data: %s", data)
                data_atualizacao = Timestamp.now()
                query = f"""
                    INSERT INTO `{table_id}` (config, data_ultima_atualizacao, id_empresa)
                    VALUES (@config, @data_ultima_atualizacao, @id_empresa)
                """
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter(
                            "config",
                            "STRING",
                            json.dumps(data.get("config"))
                            ),
                        bigquery.ScalarQueryParameter(
                            "data_ultima_atualizacao",
                            "TIMESTAMP",
                            data_atualizacao
                            ),
                        bigquery.ScalarQueryParameter(
                            "id_empresa",
                            "STRING",
                            self.id_empresa
                            ),
                    ]
                )
                self._save_dataframe_to_bq(query, job_config=job_config)
        except Exception as e:
            logger.error("Erro ao criar config: %s", e)

    @log_func_time
    def update_config(self, data):
        """Atualiza um config no BigQuery e no Redis."""
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.configuration"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                redis_client_set(f"config:{self.id_empresa}", json.dumps(data))
                logger.info("Atualizando config...")
                data_atualizacao = Timestamp.now()
                query = f"""
                    UPDATE `{table_id}`
                    SET config = @config,
                        data_ultima_atualizacao = @data_ultima_atualizacao
                    WHERE id_empresa = @id_empresa
                """
                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter(
                            "config",
                            "STRING",
                            json.dumps(data.get("config"))
                            ),
                        bigquery.ScalarQueryParameter(
                            "data_ultima_atualizacao",
                            "TIMESTAMP",
                            data_atualizacao
                            ),
                        bigquery.ScalarQueryParameter(
                            "id_empresa",
                            "STRING",
                            self.id_empresa
                            ),
                    ]
                )
                self._save_dataframe_to_bq(query, job_config=job_config)
        except Exception as e:
            logger.error("Erro ao atualizar config: %s", e)

    @log_func_time
    def get_config(self) -> str:
        """Obtém um config do BigQuery ou do Redis."""
        try:
            if check_if_key_exists(f"config:{self.id_empresa}"):
                config = redis_client_get(f"config:{self.id_empresa}")
                return json.loads(config)
            logger.info("Obtendo config...")
            result = self._execute_query(f"SELECT config FROM {GCP_BIGQUERY_DATASET}.configuration")
            result = result.to_dataframe()
            if result.empty:
                return {}
            config = result.iloc[0]['config']
            return json.loads(config)
        except Exception as e:
            logger.error("Erro ao obter config: %s", e)
            return {}

    @log_func_time
    def delete_config(self):
        """Deleta um config do BigQuery e do Redis."""
        try:
            if check_if_key_exists(f"config:{self.id_empresa}"):
                connections.redis_client.delete(f"config:{self.id_empresa}")
            logger.info("Deletando config...")
            table_id = f"{GCP_BIGQUERY_DATASET}.configuration"
            query = f"DELETE FROM {table_id}"
            self._execute_query(query)
        except Exception as e:
            logger.error("Erro ao deletar config: %s", e)

