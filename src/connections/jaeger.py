"""Module for the <PERSON><PERSON><PERSON> connection."""
import os

from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import J<PERSON>gerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.trace.sampling import <PERSON><PERSON>, <PERSON>plingResult, Decision
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes

JAEGER_HOST = os.getenv("JAEGER_HOST", "jaeger")
JAEGER_PORT = int(os.getenv("JAEGER_PORT", 6831))

class JaegerConnection:
    """
    Class to handle the connection with <PERSON><PERSON><PERSON>
    """

    _instance = None
    _tracer = None

    def __new__(cls) -> "JaegerConnection":
        if cls._instance is None:
            cls._instance = super(JaegerConnection, cls).__new__(cls)
        return cls._instance

    def connect(self, service_name: str) -> trace.Tracer:
        """
        Establishes connection with <PERSON><PERSON><PERSON> using the configuration from docker-compose

        :param service_name: Name of the service to be traced
        """
        if self._tracer is None:
            resource = Resource.create({ResourceAttributes.SERVICE_NAME: service_name})

            jaeger_exporter = JaegerExporter(
                agent_host_name=JAEGER_HOST,
                agent_port=JAEGER_PORT,
            )
            exclude_traces_sampler = ExcludeTracesSampler(["BigQuery"])

            tracer_provider = TracerProvider(sampler=exclude_traces_sampler, resource=resource)
            tracer_provider.add_span_processor(BatchSpanProcessor(jaeger_exporter))

            trace.set_tracer_provider(tracer_provider)
            self._tracer = trace.get_tracer(service_name)

        return self._tracer

    def get_tracer(self) -> trace.Tracer:
        """
        Returns the configured tracer
        """
        if not self._tracer:
            raise Exception("Tracer not connected. Call connect() method first.")
        return self._tracer


class ExcludeTracesSampler(Sampler):
    def __init__(self, excluded_attributes: list):
        super().__init__()
        self.excluded_attributes = excluded_attributes

    def should_sample(self, parent_context, trace_id, name, kind, attributes, links):
        if attributes and attributes.get("db.system") in self.excluded_attributes:
            return SamplingResult(Decision.DROP, attributes, None)
        return SamplingResult(Decision.RECORD_AND_SAMPLE, attributes, None)

    def get_description(self):
        return "ExcludeTracesSampler"
