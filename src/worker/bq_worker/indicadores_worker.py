import logging
import json

from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import WorkersTracer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="BQ indicadores Worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")

    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        task = redis_client.brpop('indicators_queue')
        if task:
            task = json.loads(task[1].decode('utf-8'))
            data = task.get('data', {})
            id_empresa = data.get('id_empresa', None)
            indicador = data.get('indicador', None)
            if data == {}:
                continue
            bq_ = bq(id_empresa=id_empresa)
            bq_.register_indicator(data, indicador)
            logger.info(f" [x] Indicador '{indicador}' registrado.")

        iter_count += 1
