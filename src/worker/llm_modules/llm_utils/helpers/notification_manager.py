"""
Módulo de gerencia de regras de negócio de notificações agendadas no geral
"""

import json
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any

from src.data.bigquery_data import BigQueryData
from src.worker.entrypoint import connections
from src.data.bigquery_data import get_from_empresa
from src.extras.util import get_device_from_z_api

logger = logging.getLogger("conversas_logger")
scheduler_queue = os.environ.get("RQ_QUEUE_NAME", "scheduler_queue")


class NotificationManager:
    """
    Classe responsável por gerenciar as notificações agendadas.
    A classe é incializada com o id_empresa e com todos os parâmetros necessários.
    O método schedule é responsável por agendar as notificações.

    Exemplo de uso:
    - Crie um novo agendamento como método da classe:
    ```python
    class NotificationManager:
        ...

        def xpto(self, arg1, arg2):
            # Lógica do agendamento
            # Em algum momento aqui podem ser usados argumentos
            ... self.a ...
            ... self.b ...
            ... self.c ...
            pass
    ```
    - Chame o método schedule:
    ```python
    NotificationManager(
        "<id_empresa>",
        # Estes atributos adicionais ficarão disponíveis para uso como:
        # self.a, self.b, self.c
        class_attributes={
            "a": "<valor_a>",
            "b": "<valor_b>",
            "c": "<valor_c>",
        },
    ).schedule(
        "xpto",
        kwargs={
            "arg1": "valor_arg1",
            "arg2": "valor_arg2",
        }
    )
    ```
    - Se a classe for mal configurada, ela **irá levantar um erro**, isto é proposital.
    """

    def __init__(self, id_empresa: str, class_attributes: dict = {}):
        self.bq = BigQueryData(id_empresa)
        self.id_empresa = id_empresa
        for k, v in class_attributes.items():
            setattr(self, k, v)

    def schedule(self, for_: str = "book_class", kwargs: dict = {}):
        try:
            return getattr(self, f"for_{for_}")(**kwargs)
        except TypeError as e:
            logger.error("Dicionário de argumentos mal configurado!")
            raise TypeError from e

    def for_phase_message_send(
        self, phase: str, name: str
    ):
        phone = self.telefone
        key = f"notification_scheme:phase_message_send:scheduled:{phase}:{self.id_empresa}:{phone}"
        # Se o aluno já tiver agendamento para esta aula, remove o antigo
        if connections.redis_client.exists(
            f"{key}:jobs"
        ):
            # Remove o agendamento antigo
            connections.redis_client.lpush(
                scheduler_queue,
                json.dumps({
                    "action": "unschedule",
                    "key": key,
                }),
            )

        phase_schema = self.bq.get_notification_schema("phase_message_send", category=phase)

        if not phase_schema or not isinstance(phase_schema, dict):
            logger.info(
                f"Schema de notificação não encontrado para a fase: {phase}"
            )
            return

        if not (phase_schema := phase_schema.get("schema")):
            logger.info(
                f"Schema de notificação não encontrado para a fase: {phase}"
            )
            return

        max_days_after = sorted(
            phase_schema, key=lambda x: x.get("days_after")
        )[-1].get("days_after")

        logger.info(
            f"Agendando mensagens para {phase} com {max_days_after} dias de antecedência."
        )

        connected_phone = self.bq.get_connected_phone()

        if not connected_phone:
            instance_id, token = get_from_empresa(
                self.id_empresa
            )
            connected_phone = get_device_from_z_api(
                instance_id=instance_id,
                token=token,
            )
            self.bq.save_connected_phone(connected_phone)

        for i, strategy in enumerate(phase_schema):
            message = f"""
INFORMAÇÃO DO SISTEMA:\n
```\n
Você deve enviar uma notificação seguindo esta instrução:\n
{strategy.get("instruction")}\n
```
            """.strip()
            days_after = strategy.get("days_after")

            current_date = datetime.now()

            seconds = int(timedelta(days=days_after).total_seconds())
            # seconds = (i + 1) * 120

            connections.redis_client.lpush(
                scheduler_queue,
                json.dumps({
                    "seconds": seconds,
                    "key": key,
                    "kwargs": {
                        "msg": message,
                        "id_empresa": self.id_empresa,
                        "instanceId": get_from_empresa(self.id_empresa)[0],
                        "connectedPhone": connected_phone,
                        "phone": phone,
                        "isGroup": False,
                        "messageId": str(uuid.uuid4()),
                        "sessionId": None,
                        "origin": self.origin,
                        "requiresValidation": True,
                        "validationType": "situation_on_phase",
                        "validateValue": phase,
                        "indicador": f"fase_crm-{phase}",
                        "nome": name,
                        "momment": int((
                            current_date + timedelta(seconds=int(seconds))
                        ).timestamp() * 1000),
                    },
                }),
            )

    def for_book_class(
        self, book_msg, class_details, mes, dia, name
    ) -> None:
        class_time = class_details.get("content", {}).get("horarioInicio", None)
        if class_time is None:
            return book_msg + "Mas, teve erro ao verificar detalhes da aula."
        class_hour, class_minute = class_time.split(":")

        notification_schema = self.bq.get_notification_schema("book_class")

        if notification_schema is None:
            logger.info("Schema de notificação não encontrado.")
            return book_msg + "Mas, não deu pra agendar as notificações."

        current_date = datetime.now()
        future_date = datetime(
            current_date.year, mes, dia, hour=int(class_hour), minute=int(class_minute)
        )
        time_difference = future_date - current_date

        same_day = []
        for notification_config in notification_schema["same_day"][
            "notifications_after"
        ]:
            kwargs = {notification_config["unit"]: notification_config["time"]}
            message = notification_config["message_instructions"]
            same_day.append((
                    timedelta(**kwargs).total_seconds(),
                    "**Crie uma mensagem para o usuário com as instruções**:" + message,
            ))

        intermediate_days = []
        daily_contact = []
        n_days = time_difference.days
        if (
            notification_schema["intermediate_days"]
            and notification_schema["intermediate_days"]["notifications_at"]
        ):
            for i, notification_config in enumerate(
                notification_schema["intermediate_days"]["notifications_at"]
            ):
                intermediate_days.extend(
                    [current_date + timedelta(days=d + 1) for d in range(n_days)]
                )
                hour, minute = notification_config["time"].split(":")
                message = notification_config["message_instructions"]
                daily_contact.extend(
                    [
                        (
                            (datetime(
                                year=intermediate_days[i * n_days + d].year,
                                month=intermediate_days[i * n_days + d].month,
                                day=intermediate_days[i * n_days + d].day,
                                hour=int(hour),
                                minute=int(minute),
                            ) - current_date).total_seconds(),
                            "**Crie uma mensagem para o usuário com as instruções**:"
                            + message
                            + f" falta {d + 1} "
                            + "s" * (d + 1 > 1)
                            + " para sua aula.",
                        ) for d in range(n_days)
                    ]
                )

        event_day = []
        if (
            notification_schema["event_day"]
            and notification_schema["event_day"]["notifications_at"]
        ):
            for notification_config in notification_schema["event_day"][
                "notifications_at"
            ]:
                hour, minute = notification_config["time"].split(":")
                message = notification_config["message_instructions"]
                if int(class_hour) < int(hour):
                    event_day.append(((
                        datetime(
                            year=future_date.year,
                            month=future_date.month,
                            day=future_date.day,
                            hour=int(hour),
                            minute=int(minute),
                        ) - current_date).total_seconds(),
                        "**Crie uma mensagem para o usuário com as instruções**:" + message
                    ))

        event_before = []
        if (
            notification_schema["event_day"]
            and notification_schema["event_day"]["notifications_before"]
        ):
            for notification_config in notification_schema["event_day"][
                "notifications_before"
            ]:
                kwargs = {notification_config["unit"]: notification_config["time"]}
                message = notification_config["message_instructions"]
                event_before.append(((
                    (future_date - timedelta(**kwargs)) - current_date).total_seconds(),
                    "**Crie uma mensagem para o usuário com as instruções**:"
                    + message
                    + f"falta {notification_config['time']} {notification_config['unit']} para a aula."
                ))

        event_after = []
        if (
            notification_schema["event_day"]
            and notification_schema["event_day"]["notifications_after"]
        ):
            for notification_config in notification_schema["event_day"][
                "notifications_after"
            ]:
                kwargs = {notification_config["unit"]: notification_config["time"]}
                message = notification_config["message_instructions"]
                event_after.append(((
                    (future_date + timedelta(**kwargs)) - current_date).total_seconds(),
                    "**Crie uma mensagem para o usuário com as instruções**:" + message
                ))

        seconds_until_contact = (
            same_day + daily_contact + event_day + event_before + event_after
        )

        for seconds, message in seconds_until_contact:
            logger.info(f"Agendando mensagem {message} para {seconds} segundos.")

            connections.redis_client.lpush(
                scheduler_queue,
                json.dumps({
                        "seconds": seconds,
                        "kwargs": {
                            "msg": message,
                            "id_empresa": self.id_empresa,
                            "instanceId": get_from_empresa(self.id_empresa)[0],
                            "connectedPhone": self.bq.get_connected_phone(),
                            "phone": self.telefone,
                            "isGroup": self.is_group,
                            "messageId": str(uuid.uuid4()),
                            "sessionId": self.id_session,
                            "origin": self.origin,
                            "indicador": "aula_experimental",
                            "nome": name,
                            "momment": int((
                                current_date + timedelta(seconds=int(seconds))
                            ).timestamp() * 1000),
                        },
                    }),
            )


class NotificationRules:
    """
    Classe responsável por verificar se o agendamento é válido.
    Ela será útil se o seu agendamento precisar de regras de negócio.
    Exemplo de uso:
    ```python
    from src.notification_manager import NotificationRules
    rules = NotificationRules()
    rules.validate(
        for_="situation_on_phase",
        data={
            "aluno": {
                "situacao": "LEAD"
            }
        },
        value="LEADS_HOJE"
    )
    ```
    - A fase LEADS_HOJE só é válida se a situação do aluno for LEAD.
    - A fase POS_VENDA só é válida se a situação do aluno for AT.

    Essa classe foi feita com a intenção de ser extensível
    sem muitas mudanças.
    Para adicionar novas regras, basta adicionar um novo método privado
    E um novo valor ao dicionário de regras.
    Exemplo:
    ```python
    def _new_rule(self, data: dict, value: str) -> bool:
        # Lógica da nova regra
        return True
    ```
    - E adicionar a nova regra ao dicionário de regras:
    ```python
    rules = {
        ...,
        "new_rule": {
            "valor": {
                "expect": "o",
                "path": "x.p.t[0]"
            },
    }
    ```
    **Observação:**
    A ideia é validar valores dentro de dicionários, se for necessário
    validar outros tipos de dados, será necessário extender a lógica
    de validação.
    """

    def __init__(self):
        pass

    def validate(
        self, for_: str, data: dict, value: str
    ) -> bool:
        func = getattr(self, f"_{for_}", None)
        if func is None:
            logger.error(f"Função {for_} não encontrada.")
            return False

        return func(data, value)

    def _get_situation_rules(
        self, phase: str, notification_type: str
    ) -> dict:
        """
        Obtém as regras para a situação.
        :param situation: Situação a ser verificada.
        :return: Regras para a situação.
        """
        rules = {
            "situation_on_phase": {
                "LEADS_HOJE": {
                    "expect": "LEAD",
                    "path": "aluno.situacao",
                },
                "POS_VENDA": {
                    "expect": "AT",
                    "path": "aluno.situacao.codigo",
                }

            },
        }

        return rules.get(notification_type, {}).get(phase, {})

    def _situation_on_phase(
        self, data: dict, phase: str
    ) -> bool:
        """
        Verifica se o agendamento é válido para a fase.
        :param data: Dados a serem verificados.
        :param phase: Fase a ser verificada.
        :return: True se o agendamento for válido, False caso contrário.
        """
        logger.info("Validando se a situação é valida para a fase.")
        rule = self._get_situation_rules(phase, "situation_on_phase")
        logger.info(rule)
        expect = rule.get("expect")
        path = rule.get("path")
        if not expect or not path:
            logger.info(
                f"Regras de situação não encontradas para a fase: {phase}"
            )
            return False

        return self._is_valid(
            verification_data=data,
            verification_path=path,
            verification_value=expect,
        )

    def _get_nested_value(
        self, data: dict, path: str
    ) -> Any | None:
        """
        Obtém o valor de um dicionário aninhado.

        :param data: Dicionário a ser verificado.
        :param path: Caminho do dado a ser verificado.
        :return: Valor do dicionário aninhado.
        """
        keys = path.split(".")
        try:
            for key in keys:
                if '[' and ']' in key:
                    key, index = key.split('[')
                    index = int(index[:-1])
                    if key not in data or not isinstance(data[key], list):
                        return None
                    data = data[key][index]
                else:
                    if key not in data:
                        return None
                    data = data[key]
        except (KeyError, IndexError, TypeError) as e:
            logger.info("Erro ao obter valor: %s", e)
            return None
        logger.info("Valor obtido: %s", data)
        return data

    def _is_valid(
        self,
        verification_data: dict,
        verification_path: str,
        verification_value: str,
    ) -> bool:
        """
        Verifica se o agendamento é válido.
        :param verification_data: Dados a serem verificados.
        :param verification_path: Caminho do dado a ser verificado.
        :param verification_value: Valor a ser verificado.
        :return: True se o agendamento for válido, False caso contrário.
        """
        if not isinstance(verification_data, dict):
            logger.info("Configuração de verificação invalida! Data não é dict.")
            return False

        if not isinstance(verification_path, str):
            logger.info("Configuração de verificação invalida! Path não é str.")
            return False

        if not isinstance(verification_value, str):
            logger.info("Configuração de verificação invalida! Value não é str.")
            return False

        return self._get_nested_value(
            verification_data, verification_path
        ) == verification_value
