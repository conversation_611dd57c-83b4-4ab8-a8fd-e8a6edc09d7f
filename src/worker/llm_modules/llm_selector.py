"""<PERSON><PERSON><PERSON><PERSON> responsável por selecionar o modelo de LLM a ser utilizado."""
from src.data.data_processor import DataProcessor as dp
from src.extras.util import check_for_sensitive_data, WorkersTracer
from src.worker.llm_modules.openai.openai_response_module import OpenAIResponseModule
from src.worker.llm_modules.llama.llama_response_module import LlamaResponseModule
from src.worker.llm_modules.gemini.gemini_response_module import GeminiResponseModule
from src.data.rag import Rag


class LLMSelector:
    def __init__(
        self, user_context: dict, id_empresa: str,
        phase: str, telefone: str, first_message: bool = False,
        is_group: bool = False, id_matriz: str = None, models_source: str = "openai",
        is_rede: str = False, origin: str = "z_api", id_session: str = None,
        send_logs_to: dict | None = None,
    ) -> None:
        model_args = {
            "user_context": user_context,
            "id_empresa": id_empresa,
            "phase": phase,
            "telefone": telefone,
            "first_message": first_message,
            "is_group": is_group,
            "origin": origin,
            "id_session": id_session,
            "id_matriz": id_matriz,
            "is_rede": is_rede
        }
        if send_logs_to:
            model_args["send_logs_to"] = send_logs_to
        match models_source:
            case "openai": response_module = OpenAIResponseModule
            case "llama": response_module = LlamaResponseModule
            case "gemini": response_module = GeminiResponseModule
            case _: response_module = OpenAIResponseModule

        self.response_module: OpenAIResponseModule | LlamaResponseModule | GeminiResponseModule = response_module(**model_args)

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_response",
        span_description="Obtendo resposta do LLM"
    )
    def get_response(self, user_response=None, double_check=True) -> tuple[str, dict]:
        # rag_content = self.get_rag_content(query=user_response)
        # if rag_content:
        #     user_response = (
        #         f"{user_response}\n\n"
        #         "`ATENÇÃO: Este conteúdo não faz parte da mensagem do usuário, mas pode auxiliar na contextualização da resposta.`\n\n"
        #         f"```{rag_content}```"
        #     )
        # print(f"User response: {user_response}")

        response = self.response_module.get_response(user_response=user_response)
        has_sensitive_data, action = check_for_sensitive_data(response[0])
        if has_sensitive_data:
            if action == "ignore":
                return None, None
            elif action == "regenerate":
                if not double_check:
                    return None, None
                warning = "Atenção: Ao invés de chamar o método referente à função chamada, " + \
                            "você mandou um texto com os dados da função. Por favor, " + \
                            "chame o método correto para obter a resposta desejada."
                message = f"{warning}\n\nMensagem do usuário: {user_response}\n\nSua mensagem: {response[0]}"
                return self.get_response(user_response=message, double_check=False)

        return response

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_rag_data",
        span_description="Obtendo dados para o RAG",
        span_attributes={
            "gym_data": "gym_data",
            "plans_data": "plans_data",
            "classes_data": "classes_data",
            "products_data": "products_data"
        }
    )
    def get_rag_data(self) -> dict[str, any]:
        """Retorna o conteúdo para o RAG."""
        bq_ = self.response_module.bq
        gym_data = dp.process_gym_data(bq_.get_gym_context())
        plans_data = dp.process_plans_data(bq_.get_plans_context(), return_chunks=True)
        classes_data = dp.process_static_classes_data(bq_.get_classes_context(), return_chunks=True)
        products_data = dp.process_products_data(bq_.get_products_context(), return_chunks=True)
        datas = {
            "gym": gym_data,
            "plans": plans_data,
            "classes": classes_data,
            "products": products_data
        }
        return datas

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_rag_content",
        span_description="Obtendo conteúdo do RAG",
        span_attributes={
            "query": "query",
            "top_k": "top_k"
        }
    )
    def get_rag_content(self, query: str, top_k: int = 5) -> str:
        """Retorna o conteúdo do RAG."""
        id_empresa = self.get_id_empresa()
        rag = Rag(id_empresa=id_empresa)
        # if rag.new_collection:
        #     # Novo índice, então vamos popular com os dados do BigQuery
        #     datas = self.get_rag_data()
        #     rag.populate(datas)
        return rag.search(query, top_k)

    @WorkersTracer(
        span_name_prefix=f"{__name__}.end_conversation",
        span_description="Finalizando a conversa"
    )
    def end_conversation(self):
        """Finaliza a conversa."""
        return self.response_module.end_conversation()

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_id_empresa",
        span_description="Obtendo o ID da empresa"
    )
    def get_id_empresa(self) -> str:
        return self.response_module.id_empresa
