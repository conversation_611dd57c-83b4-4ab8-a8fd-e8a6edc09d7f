from datetime import datetime
import logging
import json
import uuid

from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.extras.util import parse_phone, WorkersTracer, register_indicator
from src.data.bigquery_data import get_from_telefone, BigQueryData
from src.worker.entrypoint import connections 

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="transfer conversation worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
        "id_empresa": "id_empresa"
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")

    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        task = redis_client.brpop('tranfer_conversation')
        if task:
            try:
                task = json.loads(task[1].decode('utf-8'))
                logger.info(f" [TRANSFER] Chegou nova task")
                contact_id = task["contactId"]
                id_empresa = task["id_empresa"]
                gbit = GymbotIntegrationTools(id_empresa)
                session_id = gbit.get_sessao_por_contato(contact_id)
                conversa_json = gbit.get_conversa(session_id)
                contato_json = gbit.get_contato(conversa_json["contactId"])
                telefone = parse_phone(contato_json[ "phoneNumber"])
                id_empresa = get_from_telefone(telefone)
                status, departaments = gbit.get_departamento()
                
                # TODO: Solução temporária, Como vamos pegar o departamento da IA?
                logger.info(f"Departamentos: {departaments}")
                ai_departament = [(departamento["id"], departamento["nome"]) for departamento in departaments if "ConversasAI" in departamento["name"]][0]

                status, response = gbit.transfer_user_departament(ai_departament[0], session_id)
                if status == 200:
                    user_data = BigQueryData(id_empresa).get_user_context(telefone) or {}
                    register_indicator(
                        "transferencia_departamento",
                        id_empresa,
                        indicador=ai_departament[1],
                        telefone=telefone,
                        nome=user_data.get("aluno", {}).get("pessoa", {}).get("nome", "")
                    )
                    logger.info("[TRANSFER] Usuário transferido para o departamento ConversasAI")
                    id_conversa = str(uuid.uuid4())
                    redis_client.set(f"current_conversation:{telefone}-{id_empresa}", id_conversa)
                    redis_client.delete(f"gym_bot:conversa:{session_id}")
                    redis_client.delete(f"gym_bot:contato:{conversa_json['contactId']}")
                    logger.info(f"Novo ID de conversa criado: {id_conversa} para telefone {telefone}")
                else:
                    logger.info("[TRANSFER] Erro ao transferir usuário para o departamento ConversasAI")
                    logger.info("[TRANSFER] Response: " + str(response))
            except Exception as e:
                logger.error(f"[TRANSFER] Erro ao transferir usuário para o departamento ConversasAI: {e}")
            logger.info(" [x] Done")

        iter_count += 1
