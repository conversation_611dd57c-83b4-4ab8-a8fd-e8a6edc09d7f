from pandas import Timestamp
import os
import requests as req
import json
import logging
from datetime import datetime, timedelta
from requests.exceptions import Timeout

from src.connections.connections import Connections
from src.data.bigquery_data import BigQueryData
from src.extras.util import register_log, register_indicator, parse_phone

connections = Connections.get_instance()
logger = logging.getLogger()

URL_PACTO_DISCOVERY_MS = os.getenv("URL_PACTO_DISCOVERY_MS")

class PactoIntegrationTools:
    def __init__(
        self, id_empresa: str, id_matriz = None, send_logs_to = None
    ) -> None:
        self.id_empresa = id_empresa
        self.id_matriz = id_matriz
        self.send_logs_to = send_logs_to
        self.bq = BigQueryData(id_empresa=id_empresa)

    def get_url(self, type_, id_empresa_):
        url = connections.redis_client.get(f"{self.id_empresa}:{type_}")
        if url:
            url = url.decode('utf-8')
        else:
            logger.info(f"Request dicovery: {URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}")
            response = req.get(f"{URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}", timeout=10)
            register_log(f"{URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}", {}, {}, "GET", response, "get_url", self.id_empresa, None, send_logs_to=self.send_logs_to)
            url = response.json().get("content", {}).get("serviceUrls", {}).get(f'{type_}', None)
            validade = int(86400 - (Timestamp.now().timestamp() % 86400))
            if url is not None:
                connections.redis_client.set(f"{self.id_empresa}:{type_}", url, ex=validade)
        return url

    def get_token_auth(self, id_empresa_):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        token_auth = connections.redis_client.get(f"{self.id_empresa}:token_auth")
        if token_auth:
            token_auth = token_auth.decode('utf-8')
        else:
            username, password = self.bq.get_pacto_data()
            if not username or not password:
                username, password = 'PACTOBR', 'G]DKG61M'

            req_body = {
                "chave": id_empresa_,
                "username": username,
                "senha": password
            }
            logger.info(f"Request: {req_body}")
            url_auth = self.get_url("loginMsUrl", id_empresa_)
            logger.info(f"request auth: {url_auth}/aut/login")
            response = req.post(f"{url_auth}/aut/login", json=req_body, headers={"Content-Type": "application/json"})
            register_log(f"{url_auth}/aut/login", req_body, {}, "POST", response, "get_token_auth", self.id_empresa, None, send_logs_to=self.send_logs_to)
            content = response.json().get("content", {})
            token_auth = content.get('token', 120)
            validade = content.get('validade', 120)
            connections.redis_client.set(f"{self.id_empresa}:token_auth", token_auth, px=validade-60)

        return token_auth

    def get_levels(self):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('treinoUrl', id_empresa_)}/prest/psec/niveis?page=0&size=100"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }

        response = req.get(url=url_micro_servicos, headers=headers)
        register_log(url_micro_servicos, {}, headers, "GET", response, "get_levels", self.id_empresa, None, send_logs_to=self.send_logs_to)
        return response.json()

    def get_user_by_id(self, id_usuario):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('contatoMsUrl', id_empresa_)}/v1/ia/contextos/aluno"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {
            "empresa": cod_empresa,
            "cliente": id_usuario,
            "contextoTreino": "false"
        }
        response = req.get(url=url_micro_servicos, headers=headers, params=params)
        register_log(url_micro_servicos, {}, headers, "GET", response, "get_user_by_id", self.id_empresa, params, send_logs_to=self.send_logs_to)
        if response.status_code == 200 and response.json().get("result", [{}]) != [{}]:
            user_context= response.json().get("result", [{}])[0]
            return user_context
        return {}

    def search_by_phone(self, phone):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url = f"{self.get_url('admMsUrl', id_empresa_)}/v1/cliente"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {
            "telefone": phone.removeprefix("+55"), # Telefone sem prefixo para evitar erro no sistema
            "page": "0",
            "size": "35",
            "empresa": cod_empresa,
            "colaborador": "null",
            "tipoColaborador": "null"
        }
        try:
            logger.info(f" [*] [messages_received_worker] Buscando usuário no Sistema Pacto com o id_empresa: {id_empresa_}")
            response = req.get(url, headers=headers, params=params, timeout=10) # algumas academias estão demorando para responder
        except:
            return False, "Erro ao buscar usuário."
        register_log(url, {}, headers, "GET", response, "search_by_phone", self.id_empresa, params, send_logs_to=self.send_logs_to)
        if len(response.json().get("content", [])) > 1:
            return False, "Mais de um usuário encontrado."
        
        user_id = response.json().get("content", [])[0].get("codigo", None) if response.json().get("content", []) != [] else None
        if user_id is None:
            return False, "Usuário não encontrado."
        
        user_data = self.get_user_by_id(user_id)
        if user_data == {}:
            return False, "Erro ao buscar usuário."
        else:
            task = {
                "type": "user",
                "id_empresa": self.id_empresa,
                "id_matriz": self.id_matriz,
                "data":
                {
                    "telefone": phone,
                    "contexto": json.dumps(user_data),
                    "fase": user_data.get("fase_crm", "LEADS_HOJE"),
                    "origin_last_update": "search_by_phone",
                }
            }
            connections.redis_client.set(
                f"{phone}-{self.id_empresa}",
                json.dumps(user_data),
                ex=8*60*60
            )
            connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            return True, user_data

    def get_payment_link(
        self, cod_usuario, next_expiration=5*24*60*60, telefone="", nome=""
    ):
        """
        Faz uma requisição para obter o link de pagamento.

        Args:
            parcelas_selecionadas (str): Código das parcelas a serem pagas, separado por vírgulas.
        """
        if value := connections.redis_client.get(
            f"link_pagamento:{self.id_empresa}:{cod_usuario}"
        ):
            return {"link": value}
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        token_auth = self.get_token_auth(id_empresa_)

        url = f"{self.get_url('zwUrl', id_empresa_)}/prest/tela-cliente"
        querystring = {"op": "linkCartao", "key": id_empresa_}

        payload = {
            "cliente": cod_usuario,
            "usuario": 2,
            "empresa": cod_empresa,
            "operacao": "pagamento",
            "todasEmAberto": None,
            "parcelasSelecionadas": None,
        }

        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }

        response = req.request(
            "POST", url, json=payload,
            headers=headers, params=querystring
        )

        register_log(
            url, payload, headers, "POST", response, "get_payment_link",
            self.id_empresa, querystring, send_logs_to=self.send_logs_to
        )

        if response.status_code == 200:
            content = response.json().get("content", None)
            # O link é valido por 5 dias
            # Next Expiration se refere a quando a próxima parcela
            # será vencida, portanto, o link deve ser valido até lá
            # E até no máximo 5 dias
            expiration = min(next_expiration, 5*24*60*60) if next_expiration is not None else 5*24*60*60
            connections.redis_client.set(
                f"link_pagamento:{self.id_empresa}:{cod_usuario}",
                content,
                ex=int(expiration)
            )
            register_indicator(
                "link_pagamento_gerado", self.id_empresa, telefone=telefone, nome=nome
            )
        else:
            content = None
        return {"link": content}

    def get_descriptions(self, tipo: str):
        logger.info("Buscando descrições para tipo %s", tipo)
        id_empresa_ = self.id_empresa.split("-")[0]
        # cod_empresa = self.id_empresa.split("-")[1]
        token_auth = self.get_token_auth(id_empresa_)
        names = []
        if tipo == "objecao":
            headers = {
                'Authorization': f'Bearer {token_auth}'
            }
            params = {
                'size': 99
            }
            url = self.get_url('contatoMsUrl', id_empresa_)
            response = req.get(
                f'{url}/v1/comum/objecao',
                params=params,
                headers=headers,
                timeout=10
            )
            register_log(url, {}, headers, "GET", response, "get_descriptions", self.id_empresa, params, send_logs_to=self.send_logs_to)
            if response.status_code != 200:
                return None

            objection_data: dict = response.json()
            content = objection_data.get("content", [])
            objecoes = {obj.get("descricao", None): obj.get("codigo", None) for obj in content}
            connections.redis_client.set(
                f"{self.id_empresa}:objecoes",
                json.dumps(objecoes),
                ex=8*60*60
            )
            names = [obj.get("descricao", None) for obj in objection_data.get("content", [])]

        elif tipo == "agendamento":
            names = [
                "aula_Experimental",
                "ligação",
                "visita"
            ]
        return names

    def get_user_by_cpf(self, cpf):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        domain = self.get_url('admMsUrl', id_empresa_) #"http://**************:8119/adm-ms"
        url = f"{domain}/v1/cliente"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
        }
        params = {
            "pesquisa": cpf,
            "page": "0",
            "size": "35",
            "empresa": cod_empresa,
            "colaborador": "null",
            "tipoColaborador": "null"
        }
        
        logger.info(f"Busca de CPF pela empresa: {id_empresa_}")

        response = req.get(url, headers=headers, params=params, timeout=5)
        register_log(url, {}, headers, "GET", response, "search_by_cpf", id_empresa_, params, send_logs_to=self.send_logs_to)
        
        return url, headers, params, response
    
    def list_rede(self, rede):
        url_oamd = f"{self.get_url('oamdUrl', rede)}/prest/infra/hV0dU9aJ8dY3oE4qL2fD6jI0jF7fD2uQ"
        response_oamd = req.post(url_oamd, params={"chaveRede": rede})
        logger.info(f"Request: {url_oamd}")
        logger.info(f"Response: {response_oamd}")
        lista_empresas = response_oamd.json().get("stringList", [])

        return lista_empresas
    
    def get_state_city(self):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info(f"Request: {URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}")
        response = req.get(f"{URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}")
        register_log(f"{URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}", {}, {}, "GET", response, "get_url", self.id_empresa, None, send_logs_to=self.send_logs_to)

        empresas = response.json().get("content", {}).get("financeiroEmpresas", [])
        cities_states = [{"city":emp["cidade"], "state":emp["estado"]} for emp in empresas]
        
        return cities_states
        
    def register_lead(
            self,
            nome,
            telefone,
            cpf,
            email = None,
            mensagem = "Lead gerado pelo Conversas.AI!"
        ) -> int | None:
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        token_auth = self.get_token_auth(id_empresa_)

        base_url = self.get_url('contatoMsUrl', id_empresa_)
        url = f"{base_url}/v1/lead"

        username, _ = self.bq.get_pacto_data()

        payload = {
            "codigoEmpresa": cod_empresa,
            "usernameResposavel": username,
            "nome": nome,
            "email": email or "",
            "telefone": telefone,
            "cpf": cpf,
            "sexo": "",
            "cep": "",
            "mensagem": mensagem,
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token_auth}",
        }

        response = req.post(url, json=payload, headers=headers, timeout=10)
        register_log(url, payload, headers, "POST", response, "register_lead", self.id_empresa, None, send_logs_to=self.send_logs_to)

        if response.status_code == 200:
            register_indicator("lead_criado", self.id_empresa, telefone=telefone, nome=nome)
            return response.json().get("content", {}).get("lead")
        else:
            return None
        
    def get_empresas_context(self):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('contatoMsUrl', id_empresa_)}/v1/ia/contextos/empresa"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {
            "empresa": cod_empresa,
            "contextoTreino": "false"
        }
        try:
            response = req.get(url=url_micro_servicos, headers=headers, params=params, timeout=10)
        except Timeout:
            return {}
        if response.status_code == 200:
            empresas_context= response.json().get("result", [{}])[0]
            return empresas_context
        return {}

    def get_planos_context(self):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('contatoMsUrl', id_empresa_)}/v1/ia/contextos/planos"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {
            "empresa": cod_empresa,
            "contextoTreino": "false"
        }
        try:
            response = req.get(url=url_micro_servicos, headers=headers, params=params, timeout=10)
        except Timeout:
            return {}
        if response.status_code == 200:
            user_context = response.json().get("result", [{}])[0]
            return user_context
        return {}

    def get_turmas_context(self):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('contatoMsUrl', id_empresa_)}/v1/ia/contextos/turmas"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {
            "empresa": cod_empresa,
            "contextoTreino": "false"
        }
        try:
            response = req.get(url=url_micro_servicos, headers=headers, params=params, timeout=10)
        except Timeout:
            return {}
        if response.status_code == 200:
            turmas_context = response.json().get("result", [{}])[0]
            return turmas_context
        return {}

    def convert_lead(
        self,
        id_lead: str,
    ) -> dict | None:
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]

        base_url = self.get_url('admMsUrl', id_empresa_)
        url = f"{base_url}/v1/cliente/lead"

        token_auth = self.get_token_auth(id_empresa_)

        payload = {
            "codigoLead": id_lead,
            "empresa": cod_empresa,
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token_auth}",
        }

        response = req.post(url, json=payload, headers=headers, timeout=10)
        register_log(url, payload, headers, "POST", response, "convert_lead", self.id_empresa, None, send_logs_to=self.send_logs_to)

        if response.status_code == 200:
            json_response = response.json()
            codigo = json_response.get("content", {}).get("codigo", None)
            if (context := self.get_user_by_id(codigo)):
                phone_consulta = context['aluno']['pessoa']['telefonesconsulta']
                nome = context['aluno']['pessoa']['nome']
                phone = str(phone_consulta).split(',', maxsplit=1)[0]
                parsed_phone = parse_phone(phone)
                register_indicator("lead_convertido", self.id_empresa, telefone=parsed_phone, nome=nome)
                return context
            else:
                return None
        else:
            return None

    def book_class(self, aula: int, dia: int, mes: int, matricula: str) -> str:
        """
        Registra um usuário em uma aula, recebe um dicionário com o contexto do usuário e a data da aula.
        Se for lead, registra com código de lead, se for aluno, registra com matrícula.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        mes = str(mes).zfill(2)
        dia = str(dia).zfill(2)
        ano = Timestamp.now().year

        url_micro_servicos = f"{self.get_url('treinoUrl', id_empresa_)}/prest/psec/agenda/turmas/{aula}/marcar-aluno"
        token_auth = self.get_token_auth(id_empresa_)
        data = f"{ano}{mes}{dia}"
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {"matricula":f"{matricula}","dia":f"{data}","autorizado":"false","acao":"AULA_EXPERIMENTAL","origem": "CONVERSAS_IA"}
        response = req.put(url_micro_servicos, headers=headers, params=params, json={})
        register_log(url_micro_servicos, {}, headers, "PUT", response, "book_class", self.id_empresa, params, send_logs_to=self.send_logs_to)

        if response.status_code == 200:
            return "Aula reservada com sucesso!"
        else:
            try:
                response = response.json()
                message = response.get("meta", {}).get("message", "Não foi possível reservar a aula.")
            except json.JSONDecodeError:
                message = response.text
            
            if "níveis exigidos" in message:
                return "O aluno não tem os níveis necessários, pergunte em qual destes ele se enquadra melhor: "

            elif "idade do aluno" in message:
                return "Confirme a data de nascimento do aluno e salve com a função save_user_birthdate."
            
            elif "já foram preenchidas" in message:
                return "Não foi possível reservar a aula, pois não há vagas na turma."
            
            elif "já está matriculado" in message:
                return "Reserva não realizada pois o aluno já está matriculado nesta aula."

            return "Não foi possível reservar a aula, o assistente provavelmente errou o dia, ou o código da aula."

    def book_call(
            self,
            hora: int,
            minuto: int,
            dia: int,
            mes: int,
            duvida: str,
            user_data: dict,
            fase: str = "LEADS_HOJE"
    ) -> tuple[str, int]:
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('zwUrl', id_empresa_)}/ai/resgisto/contato/agendamento"
        token_auth = self.get_token_auth(id_empresa_)
        codigo = user_data.get("aluno", {}).get("codigo", None)
        codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)
        if not any([codigo, codigo_lead]):
            return "Não foi possível agendar a visita.", 500
        horario = f"{str(hora).zfill(2)}:{str(minuto).zfill(2)}"
        data = Timestamp.now().replace(day=dia, month=mes).strftime('%d/%m/%Y')
        if codigo is None:
            pass
            # return "Será necessário cadastrar o aluno com a função register_visitor para tentar agendar essa ligação."
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        params = {
            "chave": id_empresa_,
        }
        req_body = {
            "observacao": duvida,
            "codigoLead": codigo_lead,
            "codigoCliente": codigo,
            "empresa": cod_empresa,
            "faseAtual": fase,
            "horario": horario,
            "tipoAgendamento": "LI",
            "dataAgendamento": data
        }
        response = req.post(url_micro_servicos, headers=headers, json=req_body, params=params)
        register_log(url_micro_servicos, req_body, headers, "POST", response, "book_call", self.id_empresa, None, send_logs_to=self.send_logs_to)
        if response.status_code == 200:
            # Para não registrar novamente meta diária
            # TODO: Essa lógica precisa estar em conformidade com a fase do usuário
            # Algumas fases necessitam registros diferentes para conclusão de metas diárias
            phone = user_data.get("aluno", {}).get("telefone", None)
            if phone:
                id_conversa = connections.redis_client.get(f"current_conversation:{phone}-{self.id_empresa}")
                if id_conversa:
                    connections.redis_client.set(
                        f"meta_diaria:{id_conversa}",
                        False,
                        ex=8*60*60
                    )

            return ("Ligação agendada com sucesso! Informe ao usuário que foi agendada "
                    f"uma ligação para o dia {data} às {horario} sobre a dúvida: {duvida}"), response.status_code
        else:
            return "Não foi possível agendar a ligação.", response.status_code
        
    def book_visit(
        self,
        hora: int,
        minuto: int,
        dia: int,
        mes: int,
        objetivo: str,
        user_data: dict,
        fase: str = "LEADS_HOJE"
    ) -> tuple[str, int]:
        """
        Agenda uma visita à academia
        """

        logger.info("Agendando a visita.")
        
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        base_url = self.get_url('zwUrl', id_empresa_)
        url_micro_servicos = f"{base_url}/ai/resgisto/contato/agendamento"
        token_auth = self.get_token_auth(id_empresa_)
        codigo = user_data.get("aluno", {}).get("codigo", None)
        codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)
        if not any([codigo, codigo_lead]):
            return "Não foi possível agendar a visita.", 500
        horario = f"{str(hora).zfill(2)}:{str(minuto).zfill(2)}"
        data = Timestamp.now().replace(day=dia, month=mes).strftime('%d/%m/%Y')

        if codigo is None:
            pass
            # return "Será necessário cadastrar o aluno com a função 
            # register_visitor para tentar agendar essa visita."
        
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        
        params = {
            "chave": id_empresa_,
        }
        
        req_body = {
            "observacao": objetivo,
            "codigoLead": codigo_lead,
            "codigoCliente": codigo,
            "empresa": cod_empresa,
            "faseAtual": fase,
            "horario": horario,
            "tipoAgendamento": "VI",
            "dataAgendamento": data
        }

        logger.info(f"id_empresa: {id_empresa_}")
        logger.info(f"objetivo: {objetivo}")
        logger.info(f"codigo_lead: {codigo_lead}")
        logger.info(f"empresa: {cod_empresa}")
        logger.info(f"fase: {fase}")
        logger.info(f"horario: {horario}")
        logger.info(f"data: {data}")
        
        response = req.post(
            url_micro_servicos, 
            headers=headers, 
            json=req_body, 
            params=params)
        
        logger.info(f"Resposta do agendamento: '{response.text}'")

        register_log(
            url_micro_servicos, 
            req_body, headers, 
            "POST", 
            response, 
            "book_visit", 
            self.id_empresa)

        if response.status_code == 200:
            # Para não registrar novamente meta diária
            # TODO: Essa lógica precisa estar em conformidade com a fase do 
            # usuário.
            # Algumas fases necessitam registros diferentes para conclusão
            # de metas diárias

            phone = user_data.get("aluno", {}).get("telefone", None)
            if phone:
                id_conversa = connections.redis_client.get(
                    f"current_conversation:{phone}-{self.id_empresa}")
                if id_conversa:
                    connections.redis_client.set(
                        f"meta_diaria:{id_conversa}", False)

            return ("Visita agendada com sucesso! Informe ao usuário que foi"
                    f"agendada uma visita para o dia {data} às {horario}"), response.status_code
        
        else:
            logger.info(f"Não foi possível agendar a visita: '{response}'")
            return "Não foi possível agendar a visita.", response.status_code

    def save_meta_diaria(
        self,
        id_conversa: str,
        classificacao: str,
        descricao_acao: str,
        fase_atual: str,
        codigo_cliente: str = None,
        codigo_lead: str = None
    ):
        """
        Salva a meta diária no sistema Pacto.
        """
        try:
            match classificacao:
                case "agendamento":
                    self.save_agendamento(id_conversa, descricao_acao, fase_atual, codigo_cliente, codigo_lead)
                case "objecao":
                    self.save_objecao(descricao_acao, fase_atual, codigo_cliente, codigo_lead)
                case "simples_registro":
                    self.save_simples_registro(descricao_acao, codigo_cliente, codigo_lead)
        except Timeout as e:
            logger.info("Erro ao salvar %s, %s", classificacao, e)

        connections.redis_client.set(
            f"meta_diaria:{id_conversa}",
            "False",
            ex=8*60*60
        )

    def save_agendamento(
        self,
        id_conversa: str,
        descricao_acao: str,
        fase_atual: str,
        codigo_cliente: str = None,
        codigo_lead: str = None,
    ):
        """
        Salva um agendamento no sistema Pacto.

        Args:
            id_conversa (str): ID da conversa.
            descricao_acao (str): Descrição da ação.
            codigo_cliente (str): Código do cliente.
            codigo_lead (str): Código do lead.

        ### Funcionamento:
            - Busca um registro simples do Redis.
            - Se houver registro mais recente, salva um agendamento com base nele.
            - Se não houver registro mais recente, salva um registro simples.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('zwUrl', id_empresa_)}/ai/resgisto/contato/agendamento"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        params = {
            "chave": id_empresa_
        }
        registro_mais_recente = connections.redis_client.get(f"registros_meta_diaria:{id_conversa}")
        if not registro_mais_recente:
            return self.save_simples_registro(
                descricao_acao,
                codigo_cliente,
                codigo_lead
            )
        registro_mais_recente = json.loads(registro_mais_recente)
        tipo = registro_mais_recente.get("tipo", None) # EA, LI, VI
        horario = registro_mais_recente.get("horario", None)
        data = registro_mais_recente.get("data", None)
        modalidade = registro_mais_recente.get("modalidade", None)
        tipo_professor = registro_mais_recente.get("tipo_professor", None)
        codigo_professor = registro_mais_recente.get("codigo_professor", None)
        body = {
            "observacao": descricao_acao,
            "codigoLead": codigo_lead,
            "codigoCliente": codigo_cliente,
            "empresa": cod_empresa,
            "faseAtual": fase_atual,
            "horario": horario,
            "tipoAgendamento": tipo,
            "dataAgendamento": data,
        }
        if modalidade:
            body["modalidade"] = modalidade
        if tipo_professor:
            body["tipoProfessor"] = tipo_professor
        if codigo_professor:
            body["codigoProfessor"] = codigo_professor
        response = req.post(url_micro_servicos, headers=headers, json=body, params=params, timeout=10)
        register_log(url_micro_servicos, body, headers, "POST", response, "save_agendamento", self.id_empresa, None, send_logs_to=self.send_logs_to)

    def save_objecao(
        self,
        descricao_acao: str,
        fase_atual: str,
        codigo_cliente: str = None,
        codigo_lead: str = None,
    ):
        """
        Salva uma objeção no sistema Pacto.

        Args:
            descricao_acao (str): Descrição da ação.
            fase_atual (str): Fase atual.
            codigo_cliente (str): Código do cliente.
            codigo_lead (str): Código do lead.

        ### Funcionamento:
            - Busca as objeções do Redis (salvar em get_descriptions).
            - Salva a objeção com base na descrição da ação.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('zwUrl', id_empresa_)}/ai/resgisto/contato/objecao"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        params = {
            "chave": id_empresa_
        }
        objecoes = connections.redis_client.get(f"{self.id_empresa}:objecoes")
        if not objecoes:
            return
        objecoes = json.loads(objecoes)
        codigo_objecao = objecoes.get(descricao_acao, None)
        body = {
            "observacao": descricao_acao,
            "codigoLead": codigo_lead,
            "codigoCliente": codigo_cliente,
            "empresa": cod_empresa,
            "codigoObjecao": codigo_objecao,
            "faseAtual": fase_atual,
        }
        response = req.post(url_micro_servicos, headers=headers, json=body, params=params, timeout=10)
        register_log(url_micro_servicos, body, headers, "POST", response, "save_objecao", self.id_empresa, None, send_logs_to=self.send_logs_to)

    def save_simples_registro(
        self,
        descricao_acao: str,
        codigo_cliente: str = None,
        codigo_lead: str = None,
    ):
        """
        Salva um registro simples no sistema Pacto.

        Args:
            descricao_acao (str): Descrição da ação.
            codigo_cliente (str): Código do cliente.
            codigo_lead (str): Código do lead.

        ### Funcionamento:
            - Salva um registro simples com base na descrição
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.get_url('zwUrl', id_empresa_)}/ai/resgisto/contato"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        params = {
            "chave": id_empresa_
        }
        body = {
            "observacao": descricao_acao,
            "codigoLead": codigo_lead,
            "codigoCliente": codigo_cliente,
            "empresa": cod_empresa
        }
        response = req.post(url_micro_servicos, headers=headers, json=body, params=params, timeout=10)
        register_log(url_micro_servicos, body, headers, "POST", response, "save_simples_registro", self.id_empresa, None, send_logs_to=self.send_logs_to)
