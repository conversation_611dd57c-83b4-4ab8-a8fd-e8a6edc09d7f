import json
import logging
from uuid import uuid4
from datetime import datetime

from flask import Blueprint, request, jsonify, current_app

from src.api.app.tracing import tracer, inject
from src.api.app.auth.utils.auth_wrappers import verify_origin
from src.data.bigquery_data import BigQueryData as bq
from src.data.bigquery_data import get_from_instance
from src.extras.util import is_running, RoutesTracing

from src.extras.task_adapter import TaskAdapter

logger = logging.getLogger("conversas_logger")

receber_mensagem_bp = Blueprint('receber_mensagem', __name__)


@receber_mensagem_bp.route('', methods=['POST'])
@verify_origin()
@RoutesTracing(
    span_name_prefix="receber_mensagem",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def receber_mensagem(origin="z_api"):
    try:
        momento_recebimento = datetime.now().timestamp()
        logger.info("\n\n\nEntrou no receber mensagem\n\n\n")
        data = request.json

        id_empresa = data.get('chave_empresa', None)

        if not data:
            return jsonify({"error": "No data received"}), 400

        logger.info(f"request: {json.dumps(request.json, indent=2)}")

        if origin == "z_api":
            id_empresa, _ = get_from_instance(data["instanceId"])
        elif origin == "gym_bot":
            id_empresa = request.args["id_empresa"]

        if data.get("messageId", None) == "integration-test":
            id_empresa = request.args["id_empresa"]

        task = {
            "id_empresa": id_empresa,
            "data": data,
            "sessionId": None,
            "canAnswer": True,  # O task adapter pode alterar isso
            "origin": origin
        }

        task, _ = TaskAdapter(task, origin, id_empresa).new_task
        if not task:
            return jsonify({
                "failure": "task depois do taskAdapter veio vazio.",
            }), 400

        task["task_uid"] = f"receber_mensagem_{str(uuid4())[:8]}"
        task["momento_recebimento"] = momento_recebimento

        phone_number = task.get("data", {}).get("phone", "")

        logger.info(f"\n\ntask: {json.dumps(task, indent=2)}")

        bq_ = bq(id_empresa=id_empresa)
        model_source = bq_.get_model_source()
        health_check_keys = '\n'.join([key.decode('utf-8') for key in current_app.redis_client.keys('health_check:*')])
        logger.info(f"\nHealth check keys:\n{health_check_keys}")
        logger.info(f"\nModel source: {model_source}")
        logger.info(f"Is running routerllm: {is_running('src.routerllm.messages_received_worker')}\n")

        with tracer.start_as_current_span("send_message_to_worker") as span:
            span.set_attribute("id_empresa", id_empresa)
            span.set_attribute("phone", task.get("data", {}).get("phone_number", ""))
            span.set_attribute("model_source", str(model_source))
            span.set_attribute("origin", origin)
            carrier = {}
            inject(carrier)

        task["context"] = carrier

        if str(model_source) == "routerllm" and is_running("src.routerllm.messages_received_worker"):
            logger.info("Enviando mensagem para o routerllm")
            current_app.redis_client.lpush('messages_received_routerllm', json.dumps(task))
        else:
            logger.info("Enviando mensagem para o worker")
            current_app.redis_client.lpush('messages_received', json.dumps(task))
        
        return jsonify({
            "success": "success",
        }), 200
    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 500
