import logging

from flask import Blueprint, request, jsonify
from werkzeug.exceptions import NotFound

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.api.app.exception.exception import BadRequest
from src.data.bigquery_data import BigQueryData as bq
from src.data.data_processor import DataProcessor as dp
from src.extras.util import RoutesTracing, register_indicator

bi_bp = Blueprint('bi', __name__)

logger = logging.getLogger(__name__)

@bi_bp.route('/total_atendimentos', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_total_atendimentos",
    capture_body_fields=["empresa", "data_inicio", "data_fim"],
    capture_query_params=["empresa", "data_inicio", "data_fim", "total_atendimentos"],
)
def get_total_atendimentos(id_empresa: str):
    """
    Obter o total de atendimentos realizados para uma empresa pelo ID
    ---
    tags:
      - BI
    parameters:
      - name: empresa
        in: query
        type: string
        required: false
        description: ID da empresa para filtrar os itens convertidos.
      - name: data_inicio
        in: query
        type: string
        required: true
        description: Data de início para filtrar os itens convertidos.
      - name: data_fim
        in: query
        type: string
        required: true
        description: Data de fim para filtrar os itens convertidos.
    responses:
      200:
        description: Total de atendimentos realizados.
        schema:
          type: object
          properties:
            total_atendimentos:
              type: integer
              example: 150
            porcentagem_contatos_novos:
              type: number
              format: float
              example: 0.75
            contatos_frequentes:
              type: integer
              example: 100
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    if not data_inicio or not data_fim:
        raise BadRequest("Data de início e fim são obrigatórias")

    bq_ = bq(id_empresa=id_empresa)

    data = bq_.get_dados_bi(data_inicio=data_inicio,
                            data_fim=data_fim, type='total_atendimentos')
    logger.info(data)
    data = dp.format_dados_bi(data, 'total_atendimentos')

    if not data:
        raise NotFound("Empresa não encontrada")

    logger.info(data)

    return data, 200


@bi_bp.route('/frequencia_atendimento', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_frequencia_atendimento",
    capture_body_fields=["empresa", "data_inicio", "data_fim"],
    capture_query_params=["empresa", "data_inicio", "data_fim", "frequencia_atendimento"],
)
def get_frequencia_atendimento(id_empresa: str):
    """
    Obter a frequência de atendimento para uma empresa pelo ID
    ---
    tags:
      - BI
    parameters:
      - name: empresa
        in: query
        type: string
        required: false
        description: ID da empresa para filtrar os itens convertidos.
      - name: data_inicio
        in: query
        type: string
        required: true
        description: Data de início para filtrar os itens convertidos.
      - name: data_fim
        in: query
        type: string
        required: true
        description: Data de fim para filtrar os itens convertidos.
    responses:
      200:
        description: Frequência de atendimento.
        schema:
          type: object
          properties:
            horarios:
              type: object
              additionalProperties:
                type: integer
                example: 10
            dia_maior_frequencia:
              type: string
              example: "sabado"
            dia_menor_frequencia:
              type: string
              example: "segunda"
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    if not data_inicio or not data_fim:
        raise BadRequest("Data de início e fim são obrigatórias")

    bq_ = bq(id_empresa=id_empresa)

    data = bq_.get_dados_bi(
        data_inicio=data_inicio, data_fim=data_fim, type='frequencia_atendimento')
    logger.info(data)
    data = dp.format_dados_bi(data, 'frequencia_atendimento')
    logger.info(data)

    if not data:
        raise NotFound("Empresa não encontrada")

    return data, 200


@bi_bp.route('/tempo_medio_atendimento', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_tempo_medio_atendimento",
    capture_body_fields=["empresa", "data_inicio", "data_fim"],
    capture_query_params=["empresa", "data_inicio", "data_fim", "tempo_medio_atendimento"],
)
def get_tempo_medio_atendimento(id_empresa: str):
    """
    Obter o tempo médio de atendimento para uma empresa pelo ID
    ---
    tags:
      - BI
    parameters:
      - name: empresa
        in: query
        type: string
        required: false
        description: ID da empresa para filtrar os itens convertidos.
      - name: data_inicio
        in: query
        type: string
        required: true
        description: Data de início para filtrar os itens convertidos.
      - name: data_fim
        in: query
        type: string
        required: true
        description: Data de fim para filtrar os itens convertidos.
    responses:
      200:
        description: Tempo médio de atendimento.
        schema:
          type: object
          properties:
            tempo_medio_resposta:
              type: string
              example: "3min"
            tempo_medio_duracao_conversa:
              type: string
              example: "20min"
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    if not data_inicio or not data_fim:
        raise BadRequest("Data de início e fim são obrigatórias")

    bq_ = bq(id_empresa=id_empresa)

    data = bq_.get_dados_bi(data_inicio=data_inicio,
                            data_fim=data_fim, type='tempo_medio_atendimento')
    data = dp.format_dados_bi(data, 'tempo_medio_atendimento')

    if not data:
        raise NotFound("Empresa não encontrada")

    return data, 200


@bi_bp.route('/indicadores', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="register_indicator_endpoint",
    capture_body_fields=["identificador", "id_empresa", "indicador", "telefone", "nome", "meta"],
)
def register_indicator_endpoint(id_empresa: str):
    """
    Registrar um novo indicador
    ---
    tags:
      - BI
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: Dados do indicador a ser registrado
        required: true
        schema:
          type: object
          required:
            - identificador
          properties:
            identificador:
              type: string
              description: Identificador único do indicador
              example: "lead_criado"
            indicador:
              type: string
              description: Tipo do indicador
              example: "ativo"
            telefone:
              type: string
              description: Telefone do usuário relacionado ao indicador
              example: "5511999999999"
            nome:
              type: string
              description: Nome do usuário relacionado ao indicador
              example: "João Silva"
            meta:
              type: object
              description: Metadados adicionais do indicador
              example:
                origem: "api"
                campanha: "black_friday"
          example:
            identificador: "lead_criado"
            indicador: "ativo"
            telefone: "5511999999999"
            nome: "João Silva"
            meta:
              origem: "api"
              campanha: "black_friday"
    responses:
      200:
        description: Indicador registrado com sucesso
        schema:
          type: object
          properties:
            message:
              type: string
              example: "Indicador registrado com sucesso"
            data:
              type: object
              properties:
                identificador:
                  type: string
                  example: "lead_criado"
                id_empresa:
                  type: string
                  example: "123-456"
                indicador:
                  type: string
                  example: "ativo"
                telefone:
                  type: string
                  example: "5511999999999"
                nome:
                  type: string
                  example: "João Silva"
                meta:
                  type: object
                  example:
                    origem: "api"
                    campanha: "black_friday"
        examples:
          application/json:
            message: "Indicador registrado com sucesso"
            data:
              identificador: "lead_criado"
              id_empresa: "123-456"
              indicador: "ativo"
              telefone: "5511999999999"
              nome: "João Silva"
              meta:
                origem: "api"
                campanha: "black_friday"
      400:
        description: Dados inválidos
        schema:
          type: object
          properties:
            error:
              type: string
              example: "BadRequest: Campo 'identificador' é obrigatório"
      401:
        description: Token de autenticação inválido
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Unauthorized: Invalid token"
      403:
        description: Acesso negado
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Forbidden: Access denied"
      429:
        description: Muitas requisições
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Too Many Requests"
    """
    try:
        # Obter dados do corpo da requisição
        data = request.get_json()

        if not data:
            raise BadRequest("Corpo da requisição deve ser um JSON válido")

        # Validar campos obrigatórios
        identificador = data.get('identificador')
        if not identificador:
            raise BadRequest("Campo 'identificador' é obrigatório")

        if not isinstance(identificador, str) or not identificador.strip():
            raise BadRequest("Campo 'identificador' deve ser uma string não vazia")

        # Obter campos opcionais
        indicador = data.get('indicador', '')
        telefone = data.get('telefone', '')
        nome = data.get('nome', '')
        meta = data.get('meta', {})

        # Validar tipos de dados
        if not isinstance(indicador, str):
            raise BadRequest("Campo 'indicador' deve ser uma string")

        if not isinstance(telefone, str):
            raise BadRequest("Campo 'telefone' deve ser uma string")

        if not isinstance(nome, str):
            raise BadRequest("Campo 'nome' deve ser uma string")

        if not isinstance(meta, dict):
            raise BadRequest("Campo 'meta' deve ser um objeto")

        # Registrar o indicador usando a função existente
        register_indicator(
            identificador=identificador.strip(),
            id_empresa=id_empresa,
            indicador=indicador.strip(),
            telefone=telefone.strip(),
            nome=nome.strip(),
            meta=meta
        )

        logger.info(f"Indicador '{identificador}' registrado com sucesso para empresa '{id_empresa}'")

        # Retornar resposta de sucesso
        response_data = {
            "message": "Indicador registrado com sucesso",
            "data": {
                "identificador": identificador.strip(),
                "id_empresa": id_empresa,
                "indicador": indicador.strip(),
                "telefone": telefone.strip(),
                "nome": nome.strip(),
                "meta": meta
            }
        }

        return jsonify(response_data), 200

    except BadRequest as e:
        logger.warning(f"Erro de validação ao registrar indicador: {e.message}")
        raise e
    except Exception as e:
        logger.error(f"Erro interno ao registrar indicador: {str(e)}")
        raise BadRequest("Erro interno do servidor")
