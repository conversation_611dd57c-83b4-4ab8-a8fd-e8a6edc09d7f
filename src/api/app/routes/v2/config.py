from flask import Blueprint, request, current_app
import json

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import RoutesTracing

config_bp = Blueprint('config', __name__)

@config_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_config",
    span_description="Obter um config pelo ID da empresa",
    capture_body_fields=["config"]
)
def get_config(empresa: str):
    """
    Obter um config pelo ID da empresa
    ---
    definitions:
      Config:
        type: object
        properties:
            config:
              type: object
              properties:
                  desabilitarPlanos:
                    type: boolean
                    description: Desabilita contextos de planos
                    example: false
                  desabilitarProdutos:
                    type: boolean
                    description: Desabilita contextos de produtos
                    example: false
                  desabilitarTurmas:
                    type: boolean
                    description: Desabilita contextos de produtos
                    example: false
                  desabilitarAulaExperimental:
                    type: boolean
                    description: Desabilita agendamento de aula experimental
                    example: false
                  desabilitarLigacao:
                    type: boolean
                    description: Desabilita agendamento de ligação
                    example: false
                  desabilitarVisita:
                    type: boolean
                    description: Desabilita agendamento de visita
                    example: false
    tags:
      - Config
    parameters:
      - name: empresa
        in: query
        type: string
        required: true
        description: ID da empresa
        default: empresa-1
    responses:
      200:
        description: Config encontrado
        schema:
          $ref: '#/definitions/Config'
      404:
        description: Config não encontrado
    """
    bq_ = bq(id_empresa=empresa)
    config = bq_.get_config()
    if not config:
        return {"error": "Config não encontrado"}, 404

    return config, 200

@config_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="create_config",
    span_description="Adicionar um config para a empresa",
    capture_body_fields=["config"]
)
def create_config(empresa: str):
    """
    Adicionar um config para a empresa".
    ---
    tags:
      - Config
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        default: empresa-1
      - in: body
        name: body
        required: true
        description: Dados do config.
        schema:
          $ref: '#/definitions/Config'
    responses:
      201:
        description: Config criado com sucesso
      400:
        description: Dados inválidos
    """
    data = request.json

    if data == {}:
        return {"error": "Dados inválidos!"}, 400

    if not empresa:
        return {"error": "Erro ao criar config"}, 400

    task = {
        "type": "api-v2",
        "id_empresa": empresa,
        "data": data,
        "action": "create",
        "resource": "config"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": empresa
    }, 201


@config_bp.route('', methods=['PUT'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="update_config",
    span_description="Atualizar um config existente",
    capture_body_fields=["config"]
)
def update_config(empresa: str):
    """
    Atualizar um config existente
    ---
    tags:
      - Config
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        default: empresa-1
      - in: body
        name: body
        required: true
        description: Dados completos do config a serem atualizados.
        schema:
          $ref: '#/definitions/Config'
    responses:
      200:
        description: Config atualizado com sucesso
      400:
        description: Dados inválidos
    """
    required = ["config"]
    data = request.json

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    task = {
        "type": "api-v2",
        "id_empresa": empresa,
        "data": data,
        "action": "update",
        "resource": "config"
    }


    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": empresa
    }, 200

@config_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="delete_config",
    span_description="Deletar um config existente",
    capture_body_fields=["config"]
)
def delete_config(empresa: str):
    """
    Deletar um config existente
    ---
    tags:
      - Config
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        default: empresa-1
    responses:
      200:
        description: Config deletado com sucesso
    """
    task = {
        "type": "api-v2",
        "id_empresa": empresa,
        "action": "delete",
        "data": {},
        "resource": "config"
    }


    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": empresa
    }, 200
