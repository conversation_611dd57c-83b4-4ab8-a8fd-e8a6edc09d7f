import logging
from flask import Blueprint, request, jsonify
from werkzeug.exceptions import BadRequest

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.api.app.exception.exception import BadRequest as CustomBadRequest
from src.extras.util import register_indicator, RoutesTracing

indicadores_bp = Blueprint('indicadores', __name__)

logger = logging.getLogger(__name__)

@indicadores_bp.route('/', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="register_indicator_endpoint",
    capture_body_fields=["identificador", "id_empresa", "indicador", "telefone", "nome", "meta"],
)
def register_indicator_endpoint(id_empresa: str):
    """
    Registrar um novo indicador
    ---
    tags:
      - Indicadores
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: Dados do indicador a ser registrado
        required: true
        schema:
          type: object
          required:
            - identificador
          properties:
            identificador:
              type: string
              description: Identificador único do indicador
              example: "lead_criado"
            indicador:
              type: string
              description: Tipo do indicador
              example: "ativo"
            telefone:
              type: string
              description: Telefone do usuário relacionado ao indicador
              example: "5511999999999"
            nome:
              type: string
              description: Nome do usuário relacionado ao indicador
              example: "João Silva"
            meta:
              type: object
              description: Metadados adicionais do indicador
              example:
                origem: "api"
                campanha: "black_friday"
          example:
            identificador: "lead_criado"
            indicador: "ativo"
            telefone: "5511999999999"
            nome: "João Silva"
            meta:
              origem: "api"
              campanha: "black_friday"
    responses:
      200:
        description: Indicador registrado com sucesso
        schema:
          type: object
          properties:
            message:
              type: string
              example: "Indicador registrado com sucesso"
            data:
              type: object
              properties:
                identificador:
                  type: string
                  example: "lead_criado"
                id_empresa:
                  type: string
                  example: "123-456"
                indicador:
                  type: string
                  example: "ativo"
                telefone:
                  type: string
                  example: "5511999999999"
                nome:
                  type: string
                  example: "João Silva"
                meta:
                  type: object
                  example:
                    origem: "api"
                    campanha: "black_friday"
        examples:
          application/json:
            message: "Indicador registrado com sucesso"
            data:
              identificador: "lead_criado"
              id_empresa: "123-456"
              indicador: "ativo"
              telefone: "5511999999999"
              nome: "João Silva"
              meta:
                origem: "api"
                campanha: "black_friday"
      400:
        description: Dados inválidos
        schema:
          type: object
          properties:
            error:
              type: string
              example: "BadRequest: Campo 'identificador' é obrigatório"
      401:
        description: Token de autenticação inválido
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Unauthorized: Invalid token"
      403:
        description: Acesso negado
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Forbidden: Access denied"
      429:
        description: Muitas requisições
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Too Many Requests"
    """
    try:
        # Obter dados do corpo da requisição
        data = request.get_json()
        
        if not data:
            raise CustomBadRequest("Corpo da requisição deve ser um JSON válido")
        
        # Validar campos obrigatórios
        identificador = data.get('identificador')
        if not identificador:
            raise CustomBadRequest("Campo 'identificador' é obrigatório")
        
        if not isinstance(identificador, str) or not identificador.strip():
            raise CustomBadRequest("Campo 'identificador' deve ser uma string não vazia")
        
        # Obter campos opcionais
        indicador = data.get('indicador', '')
        telefone = data.get('telefone', '')
        nome = data.get('nome', '')
        meta = data.get('meta', {})
        
        # Validar tipos de dados
        if not isinstance(indicador, str):
            raise CustomBadRequest("Campo 'indicador' deve ser uma string")
        
        if not isinstance(telefone, str):
            raise CustomBadRequest("Campo 'telefone' deve ser uma string")
        
        if not isinstance(nome, str):
            raise CustomBadRequest("Campo 'nome' deve ser uma string")
        
        if not isinstance(meta, dict):
            raise CustomBadRequest("Campo 'meta' deve ser um objeto")
        
        # Registrar o indicador usando a função existente
        register_indicator(
            identificador=identificador.strip(),
            id_empresa=id_empresa,
            indicador=indicador.strip(),
            telefone=telefone.strip(),
            nome=nome.strip(),
            meta=meta
        )
        
        logger.info(f"Indicador '{identificador}' registrado com sucesso para empresa '{id_empresa}'")
        
        # Retornar resposta de sucesso
        response_data = {
            "message": "Indicador registrado com sucesso",
            "data": {
                "identificador": identificador.strip(),
                "id_empresa": id_empresa,
                "indicador": indicador.strip(),
                "telefone": telefone.strip(),
                "nome": nome.strip(),
                "meta": meta
            }
        }
        
        return jsonify(response_data), 200
        
    except CustomBadRequest as e:
        logger.warning(f"Erro de validação ao registrar indicador: {e.message}")
        raise e
    except Exception as e:
        logger.error(f"Erro interno ao registrar indicador: {str(e)}")
        raise CustomBadRequest("Erro interno do servidor")
