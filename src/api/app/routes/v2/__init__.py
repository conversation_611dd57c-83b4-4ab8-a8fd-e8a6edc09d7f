"""Módulo para facilitar a importação de rotas da versão 2 da API."""
from src.api.app.routes.v2.empresa import empresa_bp
from src.api.app.routes.v2.sticker import sticker_bp
from src.api.app.routes.v2.users import users_bp
from src.api.app.routes.v2.docs import doc_bp
from src.api.app.routes.v2.bi import bi_bp
from src.api.app.routes.v2.config import config_bp
from src.api.app.routes.v2.schedule import schedule_bp
from src.api.app.routes.v2.indicadores import indicadores_bp

__all__ = [
    'empresa_bp',
    'sticker_bp',
    'doc_bp',
    'users_bp',
    'bi_bp',
    'config_bp',
    'schedule_bp',
    'indicadores_bp'
]
