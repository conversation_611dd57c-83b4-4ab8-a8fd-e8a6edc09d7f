ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-buster

WORKDIR /app

ENV TZ=${TZ:-"America/Sao_Paulo"}
ENV SMTP_USERNAME=${SMTP_USERNAME:-"<EMAIL>"}
ENV SMTP_PASSWORD=${SMTP_PASSWORD:-"a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"}

COPY ./src/monitoring/requirements.txt .
RUN pip install -r requirements.txt

COPY ./src/connections /app/src/connections
COPY ./src/monitoring /app/src/monitoring

ENV PYTHONPATH=/app:$PYTHONPATH
CMD ["python", "-u", "src/monitoring/entrypoint.py"]