#!/usr/bin/env python3
"""
Script de teste para o endpoint de indicadores.
"""
import sys
import os
import json
from unittest.mock import patch, MagicMock

# Adicionar o diretório raiz ao PYTHONPATH
sys.path.insert(0, '/home/<USER>/Projects/orion')

def test_endpoint():
    """Testa o endpoint de indicadores."""
    
    # Mock das dependências
    with patch('src.extras.config.Config') as mock_config:
        mock_config.RATE_LIMIT_ENABLED = False
        mock_config.MODE = 'api'
        mock_config.REDIS_URL = 'redis://localhost:6379/0'
        mock_config.ALLOWED_ORIGINS = ['https://api.z-api.io']
        mock_config.AUTH_ON = 'false'  # Desabilitar auth para teste
        
        with patch('src.api.app.FlaskRedis'):
            with patch('src.connections.connections.Connections.get_instance') as mock_conn:
                mock_redis = MagicMock()
                mock_conn.return_value.redis_client = mock_redis
                
                with patch('src.extras.util.register_indicator') as mock_register:
                    mock_register.return_value = None
                    
                    # Importar e criar a app
                    from src.api.app import create_app
                    app = create_app()

                    # Remover before_request para teste
                    app.before_request_funcs[None] = []

                    # Criar cliente de teste
                    with app.test_client() as client:
                        # Teste 1: Dados válidos completos
                        payload = {
                            'identificador': 'lead_criado',
                            'indicador': 'ativo',
                            'telefone': '5511999999999',
                            'nome': 'João Silva',
                            'meta': {'origem': 'api', 'campanha': 'black_friday'}
                        }
                        
                        response = client.post(
                            '/indicadores/',
                            json=payload,
                            content_type='application/json'
                        )
                        
                        print(f"Teste 1 - Status: {response.status_code}")
                        print(f"Teste 1 - Response: {response.get_json()}")
                        
                        # Teste 2: Dados mínimos
                        payload_minimal = {
                            'identificador': 'conversa_iniciada'
                        }
                        
                        response2 = client.post(
                            '/indicadores/',
                            json=payload_minimal,
                            content_type='application/json'
                        )
                        
                        print(f"Teste 2 - Status: {response2.status_code}")
                        print(f"Teste 2 - Response: {response2.get_json()}")
                        
                        # Teste 3: Dados inválidos (sem identificador)
                        payload_invalid = {
                            'indicador': 'ativo'
                        }
                        
                        response3 = client.post(
                            '/indicadores/',
                            json=payload_invalid,
                            content_type='application/json'
                        )
                        
                        print(f"Teste 3 - Status: {response3.status_code}")
                        print(f"Teste 3 - Response: {response3.get_json()}")
                        
                        # Verificar se register_indicator foi chamado
                        print(f"register_indicator foi chamado {mock_register.call_count} vezes")
                        if mock_register.call_count > 0:
                            print(f"Chamadas: {mock_register.call_args_list}")

if __name__ == '__main__':
    test_endpoint()
